require "test_helper"

class PagesControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get root_path
    assert_response :success
    assert_select "title", /MarketingAI/
  end

  test "should get about" do
    get about_path
    assert_response :success
  end

  test "should get careers" do
    get careers_path
    assert_response :success
  end

  test "should get press" do
    get press_path
    assert_response :success
  end

  test "should get partners" do
    get partners_path
    assert_response :success
  end

  test "should get investors" do
    get investors_path
    assert_response :success
  end

  test "should get help" do
    get help_path
    assert_response :success
  end

  test "should get contact" do
    get contact_us_path
    assert_response :success
  end

  test "should get blog" do
    get blog_path
    assert_response :success
  end

  test "should get case studies" do
    get case_studies_path
    assert_response :success
  end

  test "should get webinars" do
    get webinars_path
    assert_response :success
  end

  test "should get whitepapers" do
    get whitepapers_path
    assert_response :success
  end

  test "should get roi calculator" do
    get roi_calculator_path
    assert_response :success
  end

  test "should get onboarding" do
    get onboarding_path
    assert_response :success
  end

  test "should get tutorials" do
    get tutorials_path
    assert_response :success
  end

  test "should get community" do
    get community_path
    assert_response :success
  end

  test "should get features" do
    get features_path
    assert_response :success
  end

  test "should get pricing" do
    get pricing_path
    assert_response :success
  end

  test "should get security" do
    get security_path
    assert_response :success
  end

  test "should get enterprise" do
    get enterprise_path
    assert_response :success
  end

  test "should get privacy" do
    get privacy_path
    assert_response :success
  end

  test "should get terms" do
    get terms_path
    assert_response :success
  end

  test "should get cookies" do
    get cookies_path
    assert_response :success
  end

  test "should get gdpr" do
    get gdpr_path
    assert_response :success
  end

  test "should get dpa" do
    get dpa_path
    assert_response :success
  end

  test "should redirect logged in user from index to dashboard" do
    user = users(:one)
    sign_in user
    get root_path
    assert_redirected_to dashboard_path
  end

  test "should handle contact form submission" do
    post contact_us_path, params: {
      name: "Test User",
      email: "<EMAIL>",
      subject: "general",
      message: "Test message",
      privacy_consent: true
    }
    assert_redirected_to contact_us_path
    assert_equal "Thank you for your message. We'll get back to you soon!", flash[:notice]
  end

  test "should handle roi calculator submission" do
    post roi_calculator_path, params: {
      current_revenue: 100000,
      conversion_rate: 2.5,
      average_deal_size: 5000
    }
    assert_redirected_to roi_calculator_path
    assert_equal "ROI calculation completed", flash[:notice]
  end

  test "should render blog post with slug" do
    get blog_post_path("test-slug")
    assert_response :success
    # Verify the blog post slug is handled correctly in the controller
  end

  test "should render case study with slug" do
    get case_study_path("test-case-study")
    assert_response :success
    # Verify the case study slug is handled correctly in the controller
  end

  test "should render webinar with id" do
    get webinar_path(123)
    assert_response :success
    # Verify the webinar ID is handled correctly in the controller
  end

  test "should render whitepaper with id" do
    get whitepaper_path(456)
    assert_response :success
    # Verify the whitepaper ID is handled correctly in the controller
  end

  test "should render tutorial with slug" do
    get tutorial_path("getting-started")
    assert_response :success
    # Verify the tutorial slug is handled correctly in the controller
  end

  test "should render help category" do
    get help_category_path("getting-started")
    assert_response :success
    # Verify the help category is handled correctly in the controller
  end

  test "should render help article" do
    get help_article_path("getting-started", "first-steps")
    assert_response :success
    # Verify the help article is handled correctly in the controller
  end

  # Test that all public pages don't require authentication
  test "public pages should not require authentication" do
    public_paths = [
      root_path, about_path, careers_path, press_path, partners_path,
      investors_path, help_path, contact_us_path, blog_path, case_studies_path,
      webinars_path, whitepapers_path, roi_calculator_path, onboarding_path,
      tutorials_path, community_path, features_path, pricing_path,
      security_path, enterprise_path, privacy_path, terms_path,
      cookies_path, gdpr_path, dpa_path
    ]

    public_paths.each do |path|
      get path
      assert_response :success, "Failed for path: #{path}"
    end
  end
end
