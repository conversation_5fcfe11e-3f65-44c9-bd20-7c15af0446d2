require "test_helper"

class ContactMessagesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @valid_contact_params = {
      contact_message: {
        name: "<PERSON>",
        email: "<EMAIL>",
        subject: "general",
        message: "This is a test message that is long enough to pass validation.",
        privacy_consent: true,
        company: "Test Company"
      }
    }
  end

  test "should create contact message with valid params" do
    assert_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :success
    json_response = JSON.parse(response.body)
    assert_equal "success", json_response["status"]
    assert_equal "Thank you for your message! We'll get back to you soon.", json_response["message"]
  end

  test "should reject contact message with missing name" do
    @valid_contact_params[:contact_message][:name] = ""

    assert_no_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_includes json_response["errors"]["name"], "can't be blank"
  end

  test "should reject contact message with invalid email" do
    @valid_contact_params[:contact_message][:email] = "invalid_email"

    assert_no_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_includes json_response["errors"]["email"], "is invalid"
  end

  test "should reject contact message with invalid subject" do
    @valid_contact_params[:contact_message][:subject] = "invalid_subject"

    assert_no_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_includes json_response["errors"]["subject"], "is not included in the list"
  end

  test "should reject contact message with short message" do
    @valid_contact_params[:contact_message][:message] = "short"

    assert_no_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_includes json_response["errors"]["message"], "is too short (minimum is 10 characters)"
  end

  test "should reject contact message without privacy consent" do
    @valid_contact_params[:contact_message][:privacy_consent] = false

    assert_no_difference("ContactMessage.count") do
      post contact_messages_path, params: @valid_contact_params
    end

    assert_response :unprocessable_entity
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_includes json_response["errors"]["privacy_consent"], "must be accepted"
  end

  test "should handle missing required fields" do
    post contact_messages_path, params: { contact_message: {} }

    assert_response :bad_request
    json_response = JSON.parse(response.body)
    assert_equal "error", json_response["status"]
    assert_equal "Missing required fields: name, email, message, privacy_consent", json_response["message"]
  end

  test "should validate email format" do
    invalid_emails = ["", "invalid", "@example.com", "test@", "test.example"]

    invalid_emails.each do |email|
      @valid_contact_params[:contact_message][:email] = email

      assert_no_difference("ContactMessage.count") do
        post contact_messages_path, params: @valid_contact_params
      end

      assert_response :unprocessable_entity
      json_response = JSON.parse(response.body)
      assert_equal "error", json_response["status"]
    end
  end

  test "should validate privacy consent acceptance" do
    [false, nil, ""].each do |consent_value|
      @valid_contact_params[:contact_message][:privacy_consent] = consent_value

      assert_no_difference("ContactMessage.count") do
        post contact_messages_path, params: @valid_contact_params
      end

      assert_response :unprocessable_entity
      json_response = JSON.parse(response.body)
      assert_equal "error", json_response["status"]
    end
  end

  test "should enqueue contact message job on successful creation" do
    assert_enqueued_with(job: ContactMessageJob) do
      post contact_messages_path, params: @valid_contact_params
    end
  end

  test "should set ip address and user agent" do
    post contact_messages_path, params: @valid_contact_params

    contact_message = ContactMessage.last
    assert_not_nil contact_message.ip_address
    assert_not_nil contact_message.user_agent
  end

  test "should normalize email to lowercase" do
    @valid_contact_params[:contact_message][:email] = "<EMAIL>"

    post contact_messages_path, params: @valid_contact_params

    contact_message = ContactMessage.last
    assert_equal "<EMAIL>", contact_message.email
  end

  test "should accept all valid subjects" do
    valid_subjects = %w[general sales support partnership press feature bug other]

    valid_subjects.each do |subject|
      @valid_contact_params[:contact_message][:subject] = subject
      @valid_contact_params[:contact_message][:email] = "test+#{subject}@example.com"

      assert_difference("ContactMessage.count") do
        post contact_messages_path, params: @valid_contact_params
      end

      assert_response :success
    end
  end
end
