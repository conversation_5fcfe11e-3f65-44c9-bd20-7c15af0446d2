# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  name: <PERSON>
  email: <EMAIL>
  subject: general
  message: This is a test message that is long enough to pass validation.
  privacy_consent: true
  company: Test Company
  ip_address: 127.0.0.1
  user_agent: Test User Agent
  status: pending
  submitted_at: <%= Time.current %>

two:
  name: <PERSON>
  email: <EMAIL>
  subject: sales
  message: I am interested in your product and would like to learn more about pricing and features.
  privacy_consent: true
  company: Another Company
  ip_address: ***********
  user_agent: Mozilla/5.0 Test Browser
  status: in_progress
  submitted_at: <%= 1.hour.ago %>
  read_at: <%= 30.minutes.ago %>
