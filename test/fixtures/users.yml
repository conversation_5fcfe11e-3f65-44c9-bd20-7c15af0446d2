# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password') %>
  confirmed_at: <%= Time.current %>
  first_name: <PERSON>
  last_name: Doe

two:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password') %>
  confirmed_at: <%= Time.current %>
  first_name: <PERSON>
  last_name: <PERSON>
