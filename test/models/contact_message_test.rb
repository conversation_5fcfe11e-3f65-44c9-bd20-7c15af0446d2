require "test_helper"

class ContactMessageTest < ActiveSupport::TestCase
  def setup
    @valid_attributes = {
      name: "<PERSON>",
      email: "<EMAIL>",
      subject: "general",
      message: "This is a test message that is long enough to pass validation.",
      privacy_consent: true,
      ip_address: "127.0.0.1",
      user_agent: "Test User Agent"
    }
  end

  test "should be valid with valid attributes" do
    contact_message = ContactMessage.new(@valid_attributes)
    assert contact_message.valid?
  end

  test "should require name" do
    contact_message = ContactMessage.new(@valid_attributes.except(:name))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:name], "can't be blank"
  end

  test "should require name to be at least 2 characters" do
    contact_message = ContactMessage.new(@valid_attributes.merge(name: "A"))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:name], "is too short (minimum is 2 characters)"
  end

  test "should require name to be at most 100 characters" do
    contact_message = ContactMessage.new(@valid_attributes.merge(name: "A" * 101))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:name], "is too long (maximum is 100 characters)"
  end

  test "should require email" do
    contact_message = ContactMessage.new(@valid_attributes.except(:email))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:email], "can't be blank"
  end

  test "should validate email format" do
    invalid_emails = ["invalid", "@example.com", "test@", "test.example"]

    invalid_emails.each do |email|
      contact_message = ContactMessage.new(@valid_attributes.merge(email: email))
      assert_not contact_message.valid?, "#{email} should be invalid"
      assert_includes contact_message.errors[:email], "is invalid"
    end
  end

  test "should accept valid email formats" do
    valid_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    valid_emails.each do |email|
      contact_message = ContactMessage.new(@valid_attributes.merge(email: email))
      assert contact_message.valid?, "#{email} should be valid"
    end
  end

  test "should require subject" do
    contact_message = ContactMessage.new(@valid_attributes.except(:subject))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:subject], "can't be blank"
  end

  test "should validate subject inclusion" do
    valid_subjects = %w[general sales support partnership press feature bug other]

    valid_subjects.each do |subject|
      contact_message = ContactMessage.new(@valid_attributes.merge(subject: subject))
      assert contact_message.valid?, "#{subject} should be valid"
    end

    contact_message = ContactMessage.new(@valid_attributes.merge(subject: "invalid"))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:subject], "is not included in the list"
  end

  test "should require message" do
    contact_message = ContactMessage.new(@valid_attributes.except(:message))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:message], "can't be blank"
  end

  test "should require message to be at least 10 characters" do
    contact_message = ContactMessage.new(@valid_attributes.merge(message: "short"))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:message], "is too short (minimum is 10 characters)"
  end

  test "should require message to be at most 5000 characters" do
    contact_message = ContactMessage.new(@valid_attributes.merge(message: "A" * 5001))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:message], "is too long (maximum is 5000 characters)"
  end

  test "should require privacy consent" do
    contact_message = ContactMessage.new(@valid_attributes.merge(privacy_consent: false))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:privacy_consent], "must be accepted"
  end

  test "should allow blank company" do
    contact_message = ContactMessage.new(@valid_attributes.merge(company: ""))
    assert contact_message.valid?
  end

  test "should validate company length" do
    contact_message = ContactMessage.new(@valid_attributes.merge(company: "A" * 101))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:company], "is too long (maximum is 100 characters)"
  end

  test "should require ip_address" do
    contact_message = ContactMessage.new(@valid_attributes.except(:ip_address))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:ip_address], "can't be blank"
  end

  test "should require user_agent" do
    contact_message = ContactMessage.new(@valid_attributes.except(:user_agent))
    assert_not contact_message.valid?
    assert_includes contact_message.errors[:user_agent], "can't be blank"
  end

  test "should normalize email before validation" do
    contact_message = ContactMessage.create!(@valid_attributes.merge(email: "<EMAIL>"))
    assert_equal "<EMAIL>", contact_message.email
  end

  test "should set defaults before validation" do
    contact_message = ContactMessage.create!(@valid_attributes)
    assert_equal "pending", contact_message.status
    assert_not_nil contact_message.submitted_at
  end

  test "should have pending status by default" do
    contact_message = ContactMessage.new(@valid_attributes)
    assert_equal "pending", contact_message.status
  end

  test "should support status enum" do
    contact_message = ContactMessage.create!(@valid_attributes)

    assert contact_message.pending?
    contact_message.in_progress!
    assert contact_message.in_progress?
    contact_message.resolved!
    assert contact_message.resolved?
    contact_message.closed!
    assert contact_message.closed?
  end

  test "should mark as read" do
    contact_message = ContactMessage.create!(@valid_attributes)
    assert_not contact_message.read?

    contact_message.mark_as_read!
    assert contact_message.read?
    assert_not_nil contact_message.read_at
  end

  test "should not update read_at if already read" do
    contact_message = ContactMessage.create!(@valid_attributes)
    contact_message.mark_as_read!
    original_read_at = contact_message.read_at

    contact_message.mark_as_read!
    assert_equal original_read_at, contact_message.read_at
  end

  test "should return subject label" do
    contact_message = ContactMessage.new(@valid_attributes.merge(subject: "sales"))
    assert_equal "Sales Question", contact_message.subject_label

    contact_message = ContactMessage.new(@valid_attributes.merge(subject: "unknown"))
    assert_equal "Unknown", contact_message.subject_label
  end

  test "should return estimated response time" do
    {
      "sales" => "4 hours",
      "support" => "24 hours",
      "bug" => "12 hours",
      "general" => "24 hours"
    }.each do |subject, expected_time|
      contact_message = ContactMessage.new(@valid_attributes.merge(subject: subject))
      assert_equal expected_time, contact_message.estimated_response_time
    end
  end

  test "should scope recent messages" do
    old_message = ContactMessage.create!(@valid_attributes.merge(created_at: 1.week.ago))
    new_message = ContactMessage.create!(@valid_attributes.merge(email: "<EMAIL>"))

    recent_messages = ContactMessage.recent
    assert_equal new_message, recent_messages.first
    assert_equal old_message, recent_messages.last
  end

  test "should scope by subject" do
    sales_message = ContactMessage.create!(@valid_attributes.merge(subject: "sales"))
    support_message = ContactMessage.create!(@valid_attributes.merge(
      subject: "support",
      email: "<EMAIL>"
    ))

    sales_messages = ContactMessage.by_subject("sales")
    assert_includes sales_messages, sales_message
    assert_not_includes sales_messages, support_message
  end

  test "should scope unread messages" do
    read_message = ContactMessage.create!(@valid_attributes)
    read_message.mark_as_read!

    unread_message = ContactMessage.create!(@valid_attributes.merge(email: "<EMAIL>"))

    unread_messages = ContactMessage.unread
    assert_includes unread_messages, unread_message
    assert_not_includes unread_messages, read_message
  end

  test "should scope read messages" do
    read_message = ContactMessage.create!(@valid_attributes)
    read_message.mark_as_read!

    unread_message = ContactMessage.create!(@valid_attributes.merge(email: "<EMAIL>"))

    read_messages = ContactMessage.read_messages
    assert_includes read_messages, read_message
    assert_not_includes read_messages, unread_message
  end
end
