Rails.application.routes.draw do
  devise_for :users, controllers: {
    omniauth_callbacks: "users/omniauth_callbacks"
  }

  # Devise routes with custom controllers
  # devise_for :users, controllers: {
  #   registrations: 'users/registrations',
  #   sessions: 'users/sessions',
  #   passwords: 'users/passwords',
  #   omniauth_callbacks: 'users/omniauth_callbacks'
  # }

  # Handle GET requests to sign_out (for browser cache/bookmark issues)
  get "/users/sign_out", to: "application#sign_out_get"

  # Admin and management routes
  resources :users, constraints: { id: /\d+/ } do
    member do
      patch :activate
      patch :deactivate
    end
  end

  resources :tenants do
    member do
      patch :activate
      patch :deactivate
      post :switch_to
    end
  end

  # Static pages
  get "terms", to: "pages#terms", as: :terms
  get "privacy", to: "pages#privacy", as: :privacy

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Dashboard route for authenticated users
  get "dashboard", to: "dashboard#index", as: :dashboard

  # Defines the root path route ("/")
  root "pages#index"

  # Custom authentication routes for cleaner URLs
  devise_scope :user do
    get "login", to: "users/sessions#new"
    get "signup", to: "users/registrations#new"
    get "forgot-password", to: "users/passwords#new"
    get "reset-password", to: "users/passwords#edit"
  end

  # Main application routes (protected by authentication)
  authenticate :user do
    # Dashboard
    get "dashboard", to: "dashboard#index"

    # Campaigns
    resources :campaigns do
      member do
        patch :pause
        patch :resume
        post :duplicate
        get :preview
        get :analytics
      end

      collection do
        get :templates
        post :ai_generate
      end

      resources :campaign_steps, path: "steps"
    end

    # Analytics
    get "analytics", to: "analytics#index"
    namespace :analytics do
      get :campaigns
      get :contacts
      get :revenue
      get :engagement
      get :conversion
      get :export
      get :reports
    end

    # Marketing Automation
    get "automation", to: "automation#index"
    resources :automations do
      member do
        patch :activate
        patch :deactivate
        get :performance
      end

      resources :automation_steps, path: "steps"
      resources :triggers
      resources :conditions
      resources :actions
    end

    # Contacts
    resources :contacts do
      collection do
        get :import
        post :import_csv
        get :export
        get :segments
      end

      member do
        get :activity
        get :campaigns
      end
    end

    # Contact Lists and Segments
    resources :contact_lists, path: "lists"
    resources :segments

    # Content Management
    get "content", to: "content#index"
    resources :content_pieces, path: "content" do
      member do
        post :ai_enhance
        get :versions
      end
    end

    # AI Content Generator
    get "content_generator", to: "content_generator#index"
    namespace :content_generator do
      post :email_subject
      post :email_body
      post :social_post
      post :blog_post
      post :ad_copy
    end

    # Search
    get "search", to: "search#index"
    get "search/suggestions", to: "search#suggestions"

    # Notifications
    resources :notifications, only: [ :index, :show ] do
      member do
        patch :mark_as_read
      end

      collection do
        patch :mark_all_read
        delete :clear_all
      end
    end

    # User Profile and Settings
    get "profile", to: "users#show"
    resources :users, only: [ :show, :edit, :update ] do
      member do
        patch :change_password
        delete :remove_avatar
      end
    end

    # Team Management
    get "team", to: "team#index"
    resources :team_members, path: "team/members" do
      member do
        patch :change_role
        patch :resend_invitation
      end
    end

    resources :team_invitations, path: "team/invitations" do
      member do
        patch :accept
        patch :decline
      end
    end

    # Billing
    get "billing", to: "billing#index"
    namespace :billing do
      get :subscription
      patch :update_subscription
      get :invoices
      get "invoices/:id", to: "invoices#show", as: :invoice
      get "invoices/:id/download", to: "invoices#download", as: :download_invoice
      patch :update_payment_method
      post :apply_coupon
    end

    # API Keys
    resources :api_keys, path: "settings/api-keys" do
      member do
        patch :regenerate
        patch :toggle_status
      end
    end

    # Tenant/Organization Settings
    get "settings", to: "settings#index"
    namespace :settings do
      get :general
      patch :update_general
      get :branding
      patch :update_branding
      get :integrations
      get :security
      patch :update_security
      get :billing
      get :team
      get :advanced
    end

    # Tenant-specific settings
    resource :tenant_settings, path: "organization" do
      member do
        get :billing
        get :team
        get :security
        patch :update_billing
        patch :update_team_settings
        patch :update_security
      end
    end

    # Webhooks
    resources :webhooks do
      member do
        post :test
        get :logs
      end
    end

    # Integrations
    resources :integrations do
      member do
        post :connect
        delete :disconnect
        patch :sync
      end
    end
  end

  # Public/Marketing Pages (no authentication required)
  # Company pages
  get "about", to: "pages#about"
  get "careers", to: "pages#careers"
  get "press", to: "pages#press"
  get "partners", to: "pages#partners"
  get "investors", to: "pages#investors"

  # Support and Help - TODO: Create HelpController
  get "help", to: "pages#help"
  get "help/:category", to: "pages#help_category", as: :help_category
  get "help/:category/:article", to: "pages#help_article", as: :help_article
  get "contact", to: "pages#contact", as: :contact_us
  post "contact", to: "pages#create_contact"

  # Educational Content - TODO: Create respective controllers
  get "blog", to: "pages#blog"
  get "blog/:slug", to: "pages#blog_post", as: :blog_post
  get "case-studies", to: "pages#case_studies", as: :case_studies
  get "case-studies/:slug", to: "pages#case_study", as: :case_study
  get "webinars", to: "pages#webinars"
  get "webinars/:id", to: "pages#webinar", as: :webinar
  get "whitepapers", to: "pages#whitepapers"
  get "whitepapers/:id", to: "pages#whitepaper", as: :whitepaper

  # Tools - TODO: Create ToolsController
  get "roi-calculator", to: "pages#roi_calculator", as: :roi_calculator
  post "roi-calculator", to: "pages#calculate_roi"

  # Learning Resources - TODO: Create respective controllers
  get "onboarding", to: "pages#onboarding"
  get "tutorials", to: "pages#tutorials"
  get "tutorials/:slug", to: "pages#tutorial", as: :tutorial
  get "community", to: "pages#community"

  # Product Information
  get "features", to: "pages#features"
  get "pricing", to: "pages#pricing"
  get "security", to: "pages#security"
  get "enterprise", to: "pages#enterprise"

  # Legal Pages - TODO: Create LegalController or route to PagesController
  get "cookies", to: "pages#cookies"
  get "gdpr", to: "pages#gdpr"
  get "dpa", to: "pages#dpa"

  # API Documentation
  get "api-docs", to: "api_docs#index", as: :api_docs
  get "api-docs/:version", to: "api_docs#version", as: :api_docs_version

  # System Status
  get "status", to: "status#index"
  get "status.json", to: "status#json"

  # Newsletter
  post "newsletter", to: "newsletter#subscribe", as: :newsletter_subscriptions
  get "newsletter/unsubscribe/:token", to: "newsletter#unsubscribe", as: :newsletter_unsubscribe

  # Keyboard Shortcuts Help
  get "shortcuts", to: "help#shortcuts"

  # Changelog
  get "changelog", to: "changelog#index"
  get "changelog/:version", to: "changelog#show", as: :changelog_version

  # API Routes (versioned)
  namespace :api do
    namespace :v1 do
      # Authentication
      post "auth/login", to: "authentication#login"
      post "auth/refresh", to: "authentication#refresh"
      delete "auth/logout", to: "authentication#logout"

      # Resources
      resources :campaigns, only: [ :index, :show, :create, :update, :destroy ]
      resources :contacts, only: [ :index, :show, :create, :update, :destroy ]
      resources :automations, only: [ :index, :show, :create, :update, :destroy ]
      resources :analytics, only: [ :index ]

      # Webhooks
      resources :webhooks, only: [ :index, :create, :update, :destroy ]
    end
  end

  # Webhook endpoints (external)
  namespace :webhooks do
    post "mailchimp", to: "mailchimp#handle"
    post "stripe", to: "stripe#handle"
    post "sendgrid", to: "sendgrid#handle"
  end

  # Health check
  get "health", to: "health#check"
  get "health/detailed", to: "health#detailed"

  # Admin panel (for super admins)
  namespace :admin do
    root to: "dashboard#index"
    resources :tenants
    resources :users
    resources :system_settings
    resources :feature_flags
  end

  # Error pages
  get "404", to: "errors#not_found"
  get "422", to: "errors#unprocessable_entity"
  get "500", to: "errors#internal_server_error"

  # Catch-all route (should be last)
  get "*path", to: "application#not_found", constraints: lambda { |req|!req.xhr? && req.format.html? }
end
