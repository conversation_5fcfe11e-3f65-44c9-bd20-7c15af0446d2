class CreateContactMessages < ActiveRecord::Migration[8.0]
  def change
    create_table :contact_messages do |t|
      # Core contact fields
      t.string :name, null: false
      t.string :email, null: false
      t.string :company
      t.string :subject, null: false
      t.text :message, null: false
      t.boolean :privacy_consent, null: false, default: false

      # Tracking fields
      t.string :ip_address
      t.text :user_agent
      t.text :referrer
      t.string :utm_source
      t.string :utm_medium
      t.string :utm_campaign
      t.string :utm_term
      t.string :utm_content

      # Status management fields
      t.integer :status, null: false, default: 0
      t.datetime :submitted_at, null: false, default: -> { 'CURRENT_TIMESTAMP' }
      t.datetime :read_at
      t.datetime :responded_at
      t.text :internal_notes

      # Standard Rails timestamps
      t.timestamps
    end

    # Add indexes for performance optimization
    add_index :contact_messages, :email
    add_index :contact_messages, :subject
    add_index :contact_messages, :status
    add_index :contact_messages, :submitted_at
    add_index :contact_messages, :read_at
    add_index :contact_messages, :created_at
  end
end
