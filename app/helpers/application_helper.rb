module ApplicationHelper
  # Page title helper
  def page_title(title = nil)
    base_title = "MarketingAI"
    if title.present?
      "#{title} | #{base_title}"
    else
      base_title
    end
  end

  # Navigation helper for main navigation links
  def nav_link_to(text, path, options = {})
    icon = options[:icon]
    policy_check = options[:policy]
    aria_label = options[:aria_label]

    # Check policy if provided
    return unless policy_check.nil? || (respond_to?(policy_check) && send(policy_check))

    # Determine if current page
    is_current = current_page?(path) || request.path.start_with?(path.to_s.chomp("/"))

    css_classes = if is_current
      "group flex items-center px-3 py-2 text-sm font-medium text-primary bg-blue-50 border border-blue-200 rounded-lg"
    else
      "group flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200"
    end

    link_to path, class: css_classes, "aria-label": aria_label, "aria-current": (is_current ? "page" : nil) do
      content = []
      content << content_tag(:i, "", class: "#{icon} mr-2", "aria-hidden": "true") if icon
      content << text
      safe_join(content)
    end
  end

  # Mobile navigation helper
  def mobile_nav_link(text, path, icon, show = true)
    return unless show

    is_current = current_page?(path) || request.path.start_with?(path.to_s.chomp("/"))

    css_classes = if is_current
      "flex items-center px-3 py-2 text-base font-medium text-primary bg-blue-50 border border-blue-200 rounded-lg"
    else
      "flex items-center px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200"
    end

    link_to path, class: css_classes, "aria-current": (is_current ? "page" : nil) do
      content = []
      content << content_tag(:i, "", class: "#{icon} mr-3", "aria-hidden": "true") if icon
      content << text
      safe_join(content)
    end
  end

  # Dropdown link helper for user menu
  def dropdown_link(text, path, icon, show = true)
    return unless show

    link_to path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200", role: "menuitem" do
      content = []
      content << content_tag(:i, "", class: "#{icon} mr-3 text-gray-400", "aria-hidden": "true") if icon
      content << text
      safe_join(content)
    end
  end

  # User display helpers
  def current_user_display_name
    return "Guest" unless current_user
    [ current_user.first_name, current_user.last_name ].compact.join(" ").presence || current_user.email.split("@").first.humanize
  end

  def current_user_initials
    return "G" unless current_user
    name = current_user_display_name
    name.split.map(&:first).join.upcase.first(2)
  end

  # Role helpers
  def role_badge_color(role)
    case role&.to_s&.downcase
    when "admin"
      "bg-red-500"
    when "owner"
      "bg-purple-500"
    when "manager"
      "bg-blue-500"
    when "member"
      "bg-green-500"
    else
      "bg-gray-500"
    end
  end

  def role_icon(role)
    case role&.to_s&.downcase
    when "admin"
      "fas fa-crown"
    when "owner"
      "fas fa-star"
    when "manager"
      "fas fa-users"
    when "member"
      "fas fa-user"
    else
      "fas fa-user"
    end
  end

  # Notifications helper
  def notifications_aria_label
    count = user_unread_notifications.count
    if count > 0
      "Notifications (#{count} unread)"
    else
      "Notifications"
    end
  end

  # Flash message helpers
  def flash_class(type)
    case type.to_s
    when "notice", "success"
      "bg-green-50 border-green-200 text-green-800"
    when "alert", "error"
      "bg-red-50 border-red-200 text-red-800"
    when "warning"
      "bg-yellow-50 border-yellow-200 text-yellow-800"
    when "info"
      "bg-blue-50 border-blue-200 text-blue-800"
    else
      "bg-gray-50 border-gray-200 text-gray-800"
    end
  end

  def flash_icon(type)
    case type.to_s
    when "notice", "success"
      "fas fa-check-circle text-green-400"
    when "alert", "error"
      "fas fa-exclamation-circle text-red-400"
    when "warning"
      "fas fa-exclamation-triangle text-yellow-400"
    when "info"
      "fas fa-info-circle text-blue-400"
    else
      "fas fa-info-circle text-gray-400"
    end
  end

  # Theme helper methods
  def theme_class
    # Return the appropriate theme class based on user preference or system setting
    current_theme == "dark" ? "dark" : ""
  end

  def current_theme
    # For now, default to light theme
    # In the future, this could check user preferences or browser settings
    "light"
  end

  # Accessibility helper for skip links
  def skip_link(target, text)
    link_to text, target,
            class: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-white px-4 py-2 rounded-md z-50",
            tabindex: "1"
  end

  # Additional helper methods for navigation
  def dashboard?
    current_user.present?
  end

  # Mock methods for current_user associations that may not exist yet
  def mock_notifications
    OpenStruct.new(
      count: 0,
      any?: false
    )
  end

  # Safe access to user notifications
  def user_notifications
    if current_user.respond_to?(:notifications)
      current_user.notifications
    else
      mock_notifications
    end
  end

  def user_unread_notifications
    if current_user.respond_to?(:unread_notifications)
      current_user.unread_notifications
    else
      mock_notifications
    end
  end

  # Theme helpers
  def theme_class
    # Return current theme class - can be extended for dark/light mode
    "light-theme"
  end

  def current_theme
    # Return current theme - can be extended for user preferences
    "light"
  end

  # Accessibility helper for skip links
  def skip_link(target, text, options = {})
    link_to text, target, {
      class: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded z-50",
      **options
    }
  end
end
