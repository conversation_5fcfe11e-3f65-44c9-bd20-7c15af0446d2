# frozen_string_literal: true

class CampaignPolicy < ApplicationPolicy
  def index?
    user_authenticated? && same_tenant?
  end

  def show?
    user_authenticated? && same_tenant?
  end

  def create?
    user_authenticated? && same_tenant?
  end

  def update?
    user_authenticated? && same_tenant? && (owner? || admin?)
  end

  def destroy?
    user_authenticated? && same_tenant? && (owner? || admin?)
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end
  end
end
