# frozen_string_literal: true

class SearchPolicy < ApplicationPolicy
  def perform?
    user_authenticated?
  end

  def advanced?
    user_authenticated? && same_tenant?
  end

  def global?
    user_authenticated? && admin?
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end
  end
end
