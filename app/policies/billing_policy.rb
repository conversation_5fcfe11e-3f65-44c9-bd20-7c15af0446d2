# frozen_string_literal: true

class BillingPolicy < ApplicationPolicy
  def show?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def update?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def invoices?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def payment_methods?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def subscription?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end
  end
end
