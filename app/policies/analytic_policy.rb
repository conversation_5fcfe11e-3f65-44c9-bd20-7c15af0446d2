# frozen_string_literal: true

class AnalyticPolicy < ApplicationPolicy
  def index?
    user_authenticated? && same_tenant?
  end

  def show?
    user_authenticated? && same_tenant?
  end

  def export?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end
  end
end
