# frozen_string_literal: true

class ApiKeyPolicy < ApplicationPolicy
  def index?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def show?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def create?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def update?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def destroy?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  def regenerate?
    user_authenticated? && same_tenant? && (admin? || owner?)
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end
  end
end
