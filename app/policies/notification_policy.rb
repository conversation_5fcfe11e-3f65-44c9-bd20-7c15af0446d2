# frozen_string_literal: true

class NotificationPolicy < ApplicationPolicy
  def index?
    user_authenticated?
  end

  def show?
    user_authenticated? && owner?
  end

  def create?
    user_authenticated? && admin?
  end

  def update?
    user_authenticated? && owner?
  end

  def destroy?
    user_authenticated? && owner?
  end

  def mark_read?
    user_authenticated? && owner?
  end

  def mark_all_read?
    user_authenticated?
  end

  protected

  # For notifications, owner means the notification belongs to the user
  def owner?
    return false unless record.respond_to?(:user_id)
    record.user_id == user&.id
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      return scope.none unless user
      # Users can only see their own notifications
      scope.where(user_id: user.id)
    end
  end
end
