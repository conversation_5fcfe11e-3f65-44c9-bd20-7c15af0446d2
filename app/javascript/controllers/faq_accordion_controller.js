import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["trigger", "content", "icon"]

  toggle(event) {
    const trigger = event.currentTarget
    const content = trigger.nextElementSibling
    const icon = trigger.querySelector('[data-faq-accordion-target="icon"]')
    
    // Close other open items
    this.closeOthers(trigger)
    
    // Toggle current item
    if (content.classList.contains('hidden')) {
      this.open(content, icon)
      this.trackEvent('faq_opened', { 
        question: trigger.textContent.trim() 
      })
    } else {
      this.close(content, icon)
      this.trackEvent('faq_closed', { 
        question: trigger.textContent.trim() 
      })
    }
  }

  open(content, icon) {
    content.classList.remove('hidden')
    content.style.maxHeight = '0'
    content.style.overflow = 'hidden'
    content.style.transition = 'max-height 0.3s ease-out'
    
    // Force reflow
    content.offsetHeight
    
    content.style.maxHeight = content.scrollHeight + 'px'
    icon.style.transform = 'rotate(180deg)'
    
    setTimeout(() => {
      content.style.maxHeight = 'none'
      content.style.overflow = 'visible'
    }, 300)
  }

  close(content, icon) {
    content.style.maxHeight = content.scrollHeight + 'px'
    content.style.overflow = 'hidden'
    content.style.transition = 'max-height 0.3s ease-in'
    
    // Force reflow
    content.offsetHeight
    
    content.style.maxHeight = '0'
    icon.style.transform = 'rotate(0deg)'
    
    setTimeout(() => {
      content.classList.add('hidden')
      content.style.removeProperty('max-height')
      content.style.removeProperty('overflow')
      content.style.removeProperty('transition')
    }, 300)
  }

  closeOthers(currentTrigger) {
    this.triggerTargets.forEach(trigger => {
      if (trigger !== currentTrigger) {
        const content = trigger.nextElementSibling
        const icon = trigger.querySelector('[data-faq-accordion-target="icon"]')
        
        if (!content.classList.contains('hidden')) {
          this.close(content, icon)
        }
      }
    })
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'faq',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        component: 'faq_accordion',
        ...properties
      })
    }
  }
}