// app/javascript/controllers/faq_accordion_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["trigger", "content", "icon"]

  connect() {
    this.setupKeyboardNavigation()
  }

  toggle(event) {
    const trigger = event.currentTarget
    const content = trigger.nextElementSibling
    const icon = trigger.querySelector('[data-faq-accordion-target="icon"]')
    
    // Close other open items
    this.closeOthers(trigger)
    
    // Toggle current item
    if (content.classList.contains('hidden')) {
      this.open(content, icon, trigger)
    } else {
      this.close(content, icon)
    }
  }

  open(content, icon, trigger) {
    content.classList.remove('hidden')
    content.style.maxHeight = '0'
    content.style.overflow = 'hidden'
    content.style.transition = 'max-height 0.4s ease-out, opacity 0.3s ease-out'
    content.style.opacity = '0'
    
    // Force reflow
    content.offsetHeight
    
    content.style.maxHeight = content.scrollHeight + 'px'
    content.style.opacity = '1'
    icon.style.transform = 'rotate(180deg)'
    
    // Track FAQ interaction
    this.trackEvent('faq_opened', { 
      question: trigger.textContent.trim().substring(0, 100)
    })
    
    setTimeout(() => {
      content.style.maxHeight = 'none'
      content.style.overflow = 'visible'
    }, 400)
  }

  close(content, icon) {
    content.style.maxHeight = content.scrollHeight + 'px'
    content.style.overflow = 'hidden'
    content.style.transition = 'max-height 0.3s ease-in, opacity 0.2s ease-in'
    
    // Force reflow
    content.offsetHeight
    
    content.style.maxHeight = '0'
    content.style.opacity = '0'
    icon.style.transform = 'rotate(0deg)'
    
    setTimeout(() => {
      content.classList.add('hidden')
      content.style.removeProperty('max-height')
      content.style.removeProperty('overflow')
      content.style.removeProperty('transition')
      content.style.removeProperty('opacity')
    }, 300)
  }

  closeOthers(currentTrigger) {
    this.triggerTargets.forEach(trigger => {
      if (trigger !== currentTrigger) {
        const content = trigger.nextElementSibling
        const icon = trigger.querySelector('[data-faq-accordion-target="icon"]')
        
        if (!content.classList.contains('hidden')) {
          this.close(content, icon)
        }
      }
    })
  }

  setupKeyboardNavigation() {
    this.triggerTargets.forEach((trigger, index) => {
      trigger.addEventListener('keydown', (event) => {
        switch (event.key) {
          case 'Enter':
          case ' ':
            event.preventDefault()
            this.toggle(event)
            break
          case 'ArrowDown':
            event.preventDefault()
            this.focusNextTrigger(index)
            break
          case 'ArrowUp':
            event.preventDefault()
            this.focusPreviousTrigger(index)
            break
          case 'Home':
            event.preventDefault()
            this.triggerTargets[0].focus()
            break
          case 'End':
            event.preventDefault()
            this.triggerTargets[this.triggerTargets.length - 1].focus()
            break
        }
      })
    })
  }

  focusNextTrigger(currentIndex) {
    const nextIndex = currentIndex + 1 < this.triggerTargets.length ? currentIndex + 1 : 0
    this.triggerTargets[nextIndex].focus()
  }

  focusPreviousTrigger(currentIndex) {
    const prevIndex = currentIndex - 1 >= 0 ? currentIndex - 1 : this.triggerTargets.length - 1
    this.triggerTargets[prevIndex].focus()
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'faq',
        page_location: window.location.href,
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        component: 'faq_accordion',
        page: 'contact',
        ...properties
      })
    }
  }
}

// Add CSS for shake animation
const style = document.createElement('style')
style.textContent = `
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
`
document.head.appendChild(style)