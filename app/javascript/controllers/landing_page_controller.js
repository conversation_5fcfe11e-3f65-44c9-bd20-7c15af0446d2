import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["mobileMenu", "mobileMenuButton", "videoModal"]

  connect() {
    this.initializeScrollEffects()
  }

  toggleMobileMenu() {
    const menu = this.mobileMenuTarget
    const button = this.mobileMenuButtonTarget
    
    if (menu.classList.contains('hidden')) {
      menu.classList.remove('hidden')
      button.innerHTML = '<i class="fas fa-times text-xl"></i>'
      button.setAttribute('aria-expanded', 'true')
      
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden'
    } else {
      menu.classList.add('hidden')
      button.innerHTML = '<i class="fas fa-bars text-xl"></i>'
      button.setAttribute('aria-expanded', 'false')
      
      // Restore body scroll
      document.body.style.overflow = 'auto'
    }
  }

  playVideo() {
    this.videoModalTarget.classList.remove('hidden')
    document.body.style.overflow = 'hidden'
    
    // Track analytics event
    this.trackEvent('video_play', { location: 'hero_section' })
  }

  closeVideoModal() {
    this.videoModalTarget.classList.add('hidden')
    document.body.style.overflow = 'auto'
  }

  stopPropagation(event) {
    event.stopPropagation()
  }

  contactSales() {
    // In production, this would open a modal or redirect to contact form
    this.trackEvent('contact_sales_click', { plan: 'enterprise' })
    
    // For now, show alert - replace with your contact form logic
    alert('Sales contact form would open here. In production, this would redirect to your contact page or open a modal.')
  }

  scheduleDemo() {
    // In production, this would open your booking system (Calendly, etc.)
    this.trackEvent('schedule_demo_click', { location: 'cta_section' })
    
    // For now, show alert - replace with your booking system
    alert('Demo booking system would open here. In production, this would open Calendly or your booking system.')
  }

  initializeScrollEffects() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault()
        const target = document.querySelector(anchor.getAttribute('href'))
        
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
          
          // Close mobile menu if open
          if (!this.mobileMenuTarget.classList.contains('hidden')) {
            this.toggleMobileMenu()
          }
        }
      })
    })

    // Track scroll events for analytics
    let ticking = false
    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll()
          ticking = false
        })
        ticking = true
      }
    })
  }

  handleScroll() {
    const scrollPosition = window.scrollY
    const windowHeight = window.innerHeight
    
    // Add/remove navigation background based on scroll
    const nav = document.querySelector('nav')
    if (scrollPosition > 100) {
      nav.classList.add('bg-white/95')
      nav.classList.remove('bg-white/90')
    } else {
      nav.classList.add('bg-white/90')
      nav.classList.remove('bg-white/95')
    }

    // Track scroll milestones for analytics
    const milestones = [25, 50, 75, 90]
    const scrollPercent = (scrollPosition / (document.body.scrollHeight - windowHeight)) * 100
    
    milestones.forEach(milestone => {
      if (scrollPercent >= milestone && !this[`scrollMilestone${milestone}`]) {
        this[`scrollMilestone${milestone}`] = true
        this.trackEvent('scroll_milestone', { 
          percent: milestone,
          page: 'landing'
        })
      }
    })
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'landing_page',
        ...properties
      })
    }

    // Custom analytics tracking
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, properties)
    }

    // Console log for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', eventName, properties)
    }
  }
}
