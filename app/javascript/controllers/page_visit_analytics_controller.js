import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { page: String }

  connect() {
    this.trackPageView()
    this.setupScrollTracking()
    this.setupTimeOnPage()
  }

  disconnect() {
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler)
    }
    
    this.trackTimeOnPage()
  }

  trackPageView() {
    const page = this.pageValue || 'unknown'
    
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        custom_page_name: page
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.page === 'function') {
      window.analytics.page(page, {
        title: document.title,
        url: window.location.href,
        path: window.location.pathname,
        referrer: document.referrer
      })
    }
  }

  setupScrollTracking() {
    let maxScroll = 0
    const milestones = [25, 50, 75, 90]
    const tracked = new Set()

    this.scrollHandler = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      )
      
      maxScroll = Math.max(maxScroll, scrollPercent)
      
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !tracked.has(milestone)) {
          tracked.add(milestone)
          this.trackEvent('scroll_milestone', { 
            depth: milestone,
            page: this.pageValue
          })
        }
      })
    }

    window.addEventListener('scroll', this.scrollHandler, { passive: true })
  }

  setupTimeOnPage() {
    this.startTime = Date.now()
  }

  trackTimeOnPage() {
    if (this.startTime) {
      const timeOnPage = Math.round((Date.now() - this.startTime) / 1000)
      
      this.trackEvent('time_on_page', {
        duration: timeOnPage,
        page: this.pageValue
      })
    }
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'page_analytics',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, properties)
    }
  }
}