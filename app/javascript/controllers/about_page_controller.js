import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.initializeAnimations()
    this.trackPageView()
  }

  initializeAnimations() {
    // Add any page-specific animations or interactions
    this.setupParallaxEffects()
    this.initializeTeamMemberHovers()
  }

  setupParallaxEffects() {
    const elements = document.querySelectorAll('[data-parallax]')
    
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset
      const rate = scrolled * -0.5
      
      elements.forEach(element => {
        element.style.transform = `translateY(${rate}px)`
      })
    }, { passive: true })
  }

  initializeTeamMemberHovers() {
    const teamMembers = document.querySelectorAll('.group')
    
    teamMembers.forEach(member => {
      member.addEventListener('mouseenter', () => {
        // Add subtle animation or effect
        const avatar = member.querySelector('img')
        if (avatar) {
          avatar.style.transform = 'scale(1.05)'
        }
      })
      
      member.addEventListener('mouseleave', () => {
        const avatar = member.querySelector('img')
        if (avatar) {
          avatar.style.transform = 'scale(1)'
        }
      })
    })
  }

  trackPageView() {
    // Track analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'page_view', {
        event_category: 'about_page',
        page_title: 'About Us'
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.page === 'function') {
      window.analytics.page('About', {
        title: 'About MarketingAI',
        path: window.location.pathname
      })
    }
  }
}