import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "name", "email", "company", "phone", "subject", "teamSize", "message", 
    "marketingConsent", "privacy", "nameError", "emailError", "messageError", 
    "privacyError", "submit", "successMessage", "errorMessage"
  ]

  connect() {
    this.setupValidation()
    this.setupFormTracking()
    this.initializeFormEnhancements()
  }

  setupFormTracking() {
    this.trackEvent('contact_form_viewed')
    this.formStartTime = Date.now()
    this.hasInteracted = false
  }

  setupValidation() {
    // Real-time validation
    this.nameTarget.addEventListener('blur', () => this.validateName())
    this.emailTarget.addEventListener('blur', () => this.validateEmail())
    this.messageTarget.addEventListener('blur', () => this.validateMessage())
    this.privacyTarget.addEventListener('change', () => this.validatePrivacy())
    
    // Character counter for message
    this.messageTarget.addEventListener('input', () => this.updateCharacterCount())
    
    // Form interaction tracking
    const formFields = [this.nameTarget, this.emailTarget, this.companyTarget, this.phoneTarget, this.messageTarget]
    formFields.forEach(field => {
      field.addEventListener('input', () => {
        if (!this.hasInteracted) {
          this.hasInteracted = true
          this.trackEvent('form_interaction_started')
        }
      }, { once: true })
    })
  }

  initializeFormEnhancements() {
    // Add floating labels effect
    this.addFloatingLabels()
    
    // Add character count to message field
    this.addCharacterCounter()
    
    // Add form progress indicator
    this.addProgressIndicator()
  }

  submit(event) {
    event.preventDefault()
    
    if (!this.validateForm()) {
      this.trackEvent('form_validation_failed', {
        errors: this.getValidationErrors()
      })
      return
    }

    this.setSubmitState(true)
    this.clearMessages()

    const formData = new FormData(event.target)
    const data = Object.fromEntries(formData.entries())

    // Track form submission attempt
    this.trackEvent('form_submission_started', {
      subject: data.subject,
      has_company: !!data.company,
      has_phone: !!data.phone,
      team_size: data.team_size,
      form_completion_time: Math.round((Date.now() - this.formStartTime) / 1000)
    })

    fetch(event.target.action, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contact_message: data
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.handleSuccess(data)
      } else {
        this.handleError(data)
      }
    })
    .catch(error => {
      this.handleError({ message: 'Network error. Please try again.' })
      console.error('Contact form error:', error)
    })
    .finally(() => {
      this.setSubmitState(false)
    })
  }

  validateForm() {
    let isValid = true
    
    if (!this.validateName()) isValid = false
    if (!this.validateEmail()) isValid = false
    if (!this.validateMessage()) isValid = false
    if (!this.validatePrivacy()) isValid = false
    
    return isValid
  }

  validateName() {
    const name = this.nameTarget.value.trim()
    
    if (!name) {
      this.showFieldError('name', 'Name is required')
      return false
    }
    
    if (name.length < 2) {
      this.showFieldError('name', 'Name must be at least 2 characters')
      return false
    }
    
    if (name.length > 100) {
      this.showFieldError('name', 'Name must be less than 100 characters')
      return false
    }
    
    this.clearFieldError('name')
    return true
  }

  validateEmail() {
    const email = this.emailTarget.value.trim()
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    
    if (!email) {
      this.showFieldError('email', 'Email is required')
      return false
    }
    
    if (!emailRegex.test(email)) {
      this.showFieldError('email', 'Please enter a valid email address')
      return false
    }
    
    if (email.length > 255) {
      this.showFieldError('email', 'Email address is too long')
      return false
    }
    
    this.clearFieldError('email')
    return true
  }

  validateMessage() {
    const message = this.messageTarget.value.trim()
    
    if (!message) {
      this.showFieldError('message', 'Message is required')
      return false
    }
    
    if (message.length < 20) {
      this.showFieldError('message', 'Message must be at least 20 characters')
      return false
    }
    
    if (message.length > 5000) {
      this.showFieldError('message', 'Message must be less than 5000 characters')
      return false
    }
    
    this.clearFieldError('message')
    return true
  }

  validatePrivacy() {
    if (!this.privacyTarget.checked) {
      this.showFieldError('privacy', 'You must agree to the Privacy Policy and Terms of Service')
      return false
    }
    
    this.clearFieldError('privacy')
    return true
  }

  updateCharacterCount() {
    const message = this.messageTarget.value
    const counter = document.querySelector('#message-counter')
    
    if (counter) {
      const count = message.length
      const remaining = 5000 - count
      counter.textContent = `${count}/5000 characters`
      
      if (count < 20) {
        counter.className = 'text-xs text-red-500'
      } else if (remaining < 500) {
        counter.className = 'text-xs text-yellow-600'
      } else {
        counter.className = 'text-xs text-gray-500'
      }
    }
  }

  addCharacterCounter() {
    if (!document.querySelector('#message-counter')) {
      const counter = document.createElement('div')
      counter.id = 'message-counter'
      counter.className = 'text-xs text-gray-500 mt-1'
      counter.textContent = '0/5000 characters'
      this.messageTarget.parentNode.appendChild(counter)
    }
  }

  addFloatingLabels() {
    const inputs = [this.nameTarget, this.emailTarget, this.companyTarget, this.phoneTarget]
    
    inputs.forEach(input => {
      const label = input.previousElementSibling
      
      const updateLabel = () => {
        if (input.value || input === document.activeElement) {
          label.classList.add('transform', '-translate-y-1', 'scale-75')
        } else {
          label.classList.remove('transform', '-translate-y-1', 'scale-75')
        }
      }
      
      input.addEventListener('focus', updateLabel)
      input.addEventListener('blur', updateLabel)
      input.addEventListener('input', updateLabel)
      
      // Initial state
      updateLabel()
    })
  }

  addProgressIndicator() {
    const form = this.element.querySelector('form')
    const progressBar = document.createElement('div')
    progressBar.className = 'w-full bg-gray-200 rounded-full h-1 mb-6'
    progressBar.innerHTML = '<div class="bg-indigo-600 h-1 rounded-full transition-all duration-300" style="width: 0%"></div>'
    
    form.insertBefore(progressBar, form.firstChild)
    
    const updateProgress = () => {
      const fields = [this.nameTarget, this.emailTarget, this.subjectTarget, this.messageTarget, this.privacyTarget]
      const completedFields = fields.filter(field => {
        if (field.type === 'checkbox') return field.checked
        return field.value.trim().length > 0
      }).length
      
      const progress = (completedFields / fields.length) * 100
      progressBar.querySelector('div').style.width = `${progress}%`
    }
    
    const fields = [this.nameTarget, this.emailTarget, this.subjectTarget, this.messageTarget, this.privacyTarget]
    fields.forEach(field => {
      field.addEventListener('input', updateProgress)
      field.addEventListener('change', updateProgress)
    })
  }

  getValidationErrors() {
    const errors = []
    if (!this.validateName()) errors.push('name')
    if (!this.validateEmail()) errors.push('email')
    if (!this.validateMessage()) errors.push('message')
    if (!this.validatePrivacy()) errors.push('privacy')
    return errors
  }

  showFieldError(field, message) {
    const errorTarget = this[`${field}ErrorTarget`]
    const inputTarget = this[`${field}Target`]
    
    errorTarget.textContent = message
    errorTarget.classList.remove('hidden')
    inputTarget.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    inputTarget.classList.remove('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')
    
    // Shake animation for error
    inputTarget.style.animation = 'shake 0.5s ease-in-out'
    setTimeout(() => {
      inputTarget.style.animation = ''
    }, 500)
  }

  clearFieldError(field) {
    const errorTarget = this[`${field}ErrorTarget`]
    const inputTarget = this[`${field}Target`]
    
    errorTarget.classList.add('hidden')
    inputTarget.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    inputTarget.classList.add('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')
  }

  setSubmitState(submitting) {
    this.submitTarget.disabled = submitting
    
    if (submitting) {
      this.submitTarget.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending Message...'
      this.submitTarget.classList.add('opacity-75', 'cursor-not-allowed')
    } else {
      this.submitTarget.innerHTML = 'Send Message'
      this.submitTarget.classList.remove('opacity-75', 'cursor-not-allowed')
    }
  }

  handleSuccess(data) {
    this.successMessageTarget.classList.remove('hidden')
    
    // Clear form
    this.nameTarget.value = ''
    this.emailTarget.value = ''
    this.companyTarget.value = ''
    this.phoneTarget.value = ''
    this.messageTarget.value = ''
    this.privacyTarget.checked = false
    
    // Reset progress bar
    const progressBar = document.querySelector('.bg-indigo-600')
    if (progressBar) progressBar.style.width = '0%'
    
    // Reset character counter
    this.updateCharacterCount()
    
    // Track successful submission
    this.trackEvent('form_submission_success', {
      subject: data.subject,
      contact_message_id: data.contact_message_id
    })
    
    // Scroll to success message
    this.successMessageTarget.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center' 
    })
    
    // Confetti effect
    this.showConfetti()
  }

  handleError(data) {
    this.errorMessageTarget.classList.remove('hidden')
    
    if (data.errors && typeof data.errors === 'object') {
      Object.keys(data.errors).forEach(field => {
        if (this[`${field}ErrorTarget`]) {
          this.showFieldError(field, data.errors[field][0])
        }
      })
    }
    
    this.trackEvent('form_submission_failed', {
      error: data.message || 'Unknown error',
      errors: data.errors
    })
  }

  clearMessages() {
    this.successMessageTarget.classList.add('hidden')
    this.errorMessageTarget.classList.add('hidden')
  }

  showConfetti() {
    // Simple confetti effect
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7']
    
    for (let i = 0; i < 50; i++) {
      setTimeout(() => {
        const confetti = document.createElement('div')
        confetti.style.cssText = `
          position: fixed;
          width: 10px;
          height: 10px;
          background: ${colors[Math.floor(Math.random() * colors.length)]};
          left: ${Math.random() * 100}vw;
          top: -10px;
          z-index: 1000;
          pointer-events: none;
          border-radius: 50%;
        `
        
        document.body.appendChild(confetti)
        
        confetti.animate([
          { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
          { transform: `translateY(100vh) rotate(720deg)`, opacity: 0 }
        ], {
          duration: 3000,
          easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        }).onfinish = () => confetti.remove()
      }, i * 50)
    }
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'contact_form',
        form_id: 'main_contact_form',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        form: 'contact',
        ...properties
      })
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('📝 Contact Form Event:', eventName, properties)
    }
  }
}