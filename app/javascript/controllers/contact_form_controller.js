import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "name", "email", "company", "subject", "message", "privacy",
    "nameError", "emailError", "messageError", "privacyError",
    "submit", "successMessage", "errorMessage"
  ]

  connect() {
    this.setupValidation()
    this.trackFormView()
  }

  setupValidation() {
    // Real-time validation
    this.nameTarget.addEventListener('blur', () => this.validateName())
    this.emailTarget.addEventListener('blur', () => this.validateEmail())
    this.messageTarget.addEventListener('blur', () => this.validateMessage())
    this.privacyTarget.addEventListener('change', () => this.validatePrivacy())
  }

  submit(event) {
    event.preventDefault()
    
    if (!this.validateForm()) {
      this.trackEvent('form_validation_failed')
      return
    }

    this.setSubmitState(true)
    this.clearMessages()

    const formData = new FormData(event.target)
    const data = Object.fromEntries(formData.entries())

    // Track form submission attempt
    this.trackEvent('form_submission_started', {
      subject: data.subject,
      has_company: !!data.company
    })

    fetch(event.target.action, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contact_message: data
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.handleSuccess(data)
      } else {
        this.handleError(data)
      }
    })
    .catch(error => {
      this.handleError({ message: 'Network error. Please try again.' })
      console.error('Contact form error:', error)
    })
    .finally(() => {
      this.setSubmitState(false)
    })
  }

  validateForm() {
    let isValid = true
    
    if (!this.validateName()) isValid = false
    if (!this.validateEmail()) isValid = false
    if (!this.validateMessage()) isValid = false
    if (!this.validatePrivacy()) isValid = false
    
    return isValid
  }

  validateName() {
    const name = this.nameTarget.value.trim()
    
    if (!name) {
      this.showFieldError('name', 'Name is required')
      return false
    }
    
    if (name.length < 2) {
      this.showFieldError('name', 'Name must be at least 2 characters')
      return false
    }
    
    this.clearFieldError('name')
    return true
  }

  validateEmail() {
    const email = this.emailTarget.value.trim()
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    
    if (!email) {
      this.showFieldError('email', 'Email is required')
      return false
    }
    
    if (!emailRegex.test(email)) {
      this.showFieldError('email', 'Please enter a valid email address')
      return false
    }
    
    this.clearFieldError('email')
    return true
  }

  validateMessage() {
    const message = this.messageTarget.value.trim()
    
    if (!message) {
      this.showFieldError('message', 'Message is required')
      return false
    }
    
    if (message.length < 10) {
      this.showFieldError('message', 'Message must be at least 10 characters')
      return false
    }
    
    this.clearFieldError('message')
    return true
  }

  validatePrivacy() {
    if (!this.privacyTarget.checked) {
      this.showFieldError('privacy', 'You must agree to the Privacy Policy and Terms of Service')
      return false
    }
    
    this.clearFieldError('privacy')
    return true
  }

  showFieldError(field, message) {
    const errorTarget = this[`${field}ErrorTarget`]
    const inputTarget = this[`${field}Target`]
    
    errorTarget.textContent = message
    errorTarget.classList.remove('hidden')
    inputTarget.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    inputTarget.classList.remove('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')
  }

  clearFieldError(field) {
    const errorTarget = this[`${field}ErrorTarget`]
    const inputTarget = this[`${field}Target`]
    
    errorTarget.classList.add('hidden')
    inputTarget.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    inputTarget.classList.add('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500')
  }

  setSubmitState(submitting) {
    this.submitTarget.disabled = submitting
    
    if (submitting) {
      this.submitTarget.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...'
    } else {
      this.submitTarget.innerHTML = 'Send Message'
    }
  }

  handleSuccess(data) {
    this.successMessageTarget.classList.remove('hidden')
    this.successMessageTarget.classList.add('bg-green-50', 'border', 'border-green-200')
    
    // Clear form
    this.nameTarget.value = ''
    this.emailTarget.value = ''
    this.companyTarget.value = ''
    this.messageTarget.value = ''
    this.privacyTarget.checked = false
    
    // Track successful submission
    this.trackEvent('form_submission_success', {
      subject: data.subject
    })
    
    // Scroll to success message
    this.successMessageTarget.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center' 
    })
  }

  handleError(data) {
    this.errorMessageTarget.classList.remove('hidden')
    
    if (data.errors && typeof data.errors === 'object') {
      // Show field-specific errors
      Object.keys(data.errors).forEach(field => {
        if (this[`${field}ErrorTarget`]) {
          this.showFieldError(field, data.errors[field][0])
        }
      })
    }
    
    // Track failed submission
    this.trackEvent('form_submission_failed', {
      error: data.message || 'Unknown error'
    })
  }

  clearMessages() {
    this.successMessageTarget.classList.add('hidden')
    this.errorMessageTarget.classList.add('hidden')
  }

  trackFormView() {
    this.trackEvent('contact_form_viewed')
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'contact_form',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        form: 'contact',
        ...properties
      })
    }
  }
}
