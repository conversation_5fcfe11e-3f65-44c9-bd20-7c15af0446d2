/ app/javascript/controllers/scroll_reveal_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { 
    direction: String,
    distance: Number,
    duration: Number,
    delay: Number
  }

  connect() {
    this.direction = this.directionValue || 'up'
    this.distance = this.distanceValue || 50
    this.duration = this.durationValue || 800
    this.delay = this.delayValue || 0

    this.setupInitialState()
    this.createObserver()
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  setupInitialState() {
    this.element.style.opacity = '0'
    this.element.style.transition = `opacity ${this.duration}ms ease-out, transform ${this.duration}ms ease-out`
    
    switch (this.direction) {
      case 'up':
        this.element.style.transform = `translateY(${this.distance}px)`
        break
      case 'down':
        this.element.style.transform = `translateY(-${this.distance}px)`
        break
      case 'left':
        this.element.style.transform = `translateX(${this.distance}px)`
        break
      case 'right':
        this.element.style.transform = `translateX(-${this.distance}px)`
        break
      default:
        this.element.style.transform = `translateY(${this.distance}px)`
    }
  }

  createObserver() {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            this.reveal()
          }, this.delay)
          this.observer.unobserve(this.element)
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    })

    this.observer.observe(this.element)
  }

  reveal() {
    this.element.style.opacity = '1'
    this.element.style.transform = 'translateY(0) translateX(0)'
  }
}