import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  scrollToTop(event) {
    event.preventDefault()
    
    // Smooth scroll to top
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
    
    // Focus skip link for accessibility
    const skipLink = document.querySelector('#main-content')
    if (skipLink) {
      skipLink.focus()
    }
    
    // Optional: Track analytics event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'scroll_to_top', {
        event_category: 'navigation',
        event_label: 'footer'
      })
    }
  }
}