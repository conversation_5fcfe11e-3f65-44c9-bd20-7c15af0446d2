import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.setupLoadingIndicator()
    this.setupKeyboardShortcuts()
  }

  setupLoadingIndicator() {
    const indicator = document.getElementById('page-loading')
    
    document.addEventListener('turbo:before-fetch-request', () => {
      indicator?.classList.remove('hidden')
    })
    
    document.addEventListener('turbo:before-render', () => {
      indicator?.classList.add('hidden')
    })
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      // Only handle shortcuts when not in form inputs
      if (event.target.matches('input, textarea, select')) return
      
      // Cmd/Ctrl + K for search
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        const searchInput = document.querySelector('[data-search-target="input"]')
        searchInput?.focus()
      }
      
      // Cmd/Ctrl + / for help
      if ((event.metaKey || event.ctrlKey) && event.key === '/') {
        event.preventDefault()
        // Open help modal or navigate to help
        window.location.href = '/help'
      }
    })
  }
}