// app/javascript/controllers/counter_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["number"]
  static values = { target: Number }

  connect() {
    this.hasAnimated = false
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.hasAnimated) {
          this.startCounting()
          this.hasAnimated = true
        }
      })
    }, {
      threshold: 0.5
    })

    this.observer.observe(this.element)
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  startCounting() {
    const target = this.targetValue
    const duration = 2000 // 2 seconds
    const startTime = performance.now()
    const startValue = 0

    const updateCounter = (currentTime) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Use easeOutCubic for smooth animation
      const easedProgress = 1 - Math.pow(1 - progress, 3)
      const currentValue = Math.floor(startValue + (target - startValue) * easedProgress)
      
      // Format the number based on its size
      this.numberTarget.textContent = this.formatNumber(currentValue)
      
      if (progress < 1) {
        requestAnimationFrame(updateCounter)
      } else {
        this.numberTarget.textContent = this.formatNumber(target)
      }
    }

    requestAnimationFrame(updateCounter)
  }

  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    } else {
      return num.toString()
    }
  }
}