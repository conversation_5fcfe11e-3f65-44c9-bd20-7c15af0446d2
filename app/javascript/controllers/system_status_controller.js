
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["indicator"]

  connect() {
    this.checkSystemStatus()
    // Check every 5 minutes
    this.statusInterval = setInterval(() => {
      this.checkSystemStatus()
    }, 300000)
  }

  disconnect() {
    if (this.statusInterval) {
      clearInterval(this.statusInterval)
    }
  }

  async checkSystemStatus() {
    try {
      const response = await fetch('/health', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        this.updateStatus(data.status || 'operational')
      } else {
        this.updateStatus('degraded')
      }
    } catch (error) {
      this.updateStatus('outage')
      console.error('System status check failed:', error)
    }
  }

  updateStatus(status) {
    const indicator = this.indicatorTarget
    const parent = indicator.parentNode
    const statusText = parent.querySelector('span')
    
    // Remove existing classes
    indicator.classList.remove('bg-green-400', 'bg-yellow-400', 'bg-red-400', 'animate-pulse')
    
    switch (status) {
      case 'operational':
        indicator.classList.add('bg-green-400', 'animate-pulse')
        statusText.textContent = 'All Systems Operational'
        break
      case 'degraded':
        indicator.classList.add('bg-yellow-400', 'animate-pulse')
        statusText.textContent = 'Some Systems Degraded'
        break
      case 'outage':
        indicator.classList.add('bg-red-400', 'animate-pulse')
        statusText.textContent = 'System Issues Detected'
        break
      default:
        indicator.classList.add('bg-gray-400')
        statusText.textContent = 'Status Unknown'
    }
    
    // Update aria-label for accessibility
    parent.setAttribute('aria-label', `System status: ${statusText.textContent}`)
  }
}

// app/javas