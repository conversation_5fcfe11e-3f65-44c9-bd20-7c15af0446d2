import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["monthlyBtn", "yearlyBtn", "starterPrice", "professionalPrice"]

  connect() {
    this.monthlyPrices = {
      starter: 49,
      professional: 149
    }
    
    this.yearlyPrices = {
      starter: 39, // 20% discount
      professional: 119 // 20% discount
    }
    
    this.currentPlan = 'monthly'
    this.updatePrices()
  }

  selectMonthly() {
    if (this.currentPlan === 'monthly') return
    
    this.currentPlan = 'monthly'
    this.updateButtons()
    this.updatePrices()
    this.trackPricingToggle('monthly')
  }

  selectYearly() {
    if (this.currentPlan === 'yearly') return
    
    this.currentPlan = 'yearly'
    this.updateButtons()
    this.updatePrices()
    this.trackPricingToggle('yearly')
  }

  updateButtons() {
    if (this.currentPlan === 'monthly') {
      this.monthlyBtnTarget.classList.add('bg-white', 'text-gray-900', 'shadow-sm')
      this.monthlyBtnTarget.classList.remove('text-gray-600', 'hover:text-gray-900')
      
      this.yearlyBtnTarget.classList.remove('bg-white', 'text-gray-900', 'shadow-sm')
      this.yearlyBtnTarget.classList.add('text-gray-600', 'hover:text-gray-900')
    } else {
      this.yearlyBtnTarget.classList.add('bg-white', 'text-gray-900', 'shadow-sm')
      this.yearlyBtnTarget.classList.remove('text-gray-600', 'hover:text-gray-900')
      
      this.monthlyBtnTarget.classList.remove('bg-white', 'text-gray-900', 'shadow-sm')
      this.monthlyBtnTarget.classList.add('text-gray-600', 'hover:text-gray-900')
    }
  }

  updatePrices() {
    const prices = this.currentPlan === 'monthly' ? this.monthlyPrices : this.yearlyPrices
    
    // Animate price changes
    this.animatePrice(this.starterPriceTarget, prices.starter)
    this.animatePrice(this.professionalPriceTarget, prices.professional)
  }

  animatePrice(element, newPrice) {
    element.style.transform = 'scale(0.9)'
    element.style.opacity = '0.7'
    
    setTimeout(() => {
      element.textContent = `$${newPrice}`
      element.style.transform = 'scale(1)'
      element.style.opacity = '1'
    }, 150)
  }

  trackPricingToggle(plan) {
    // Track analytics event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pricing_toggle', {
        event_category: 'pricing',
        plan_type: plan
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track('Pricing Toggle', { 
        plan_type: plan,
        page: 'landing'
      })
    }
  }
}