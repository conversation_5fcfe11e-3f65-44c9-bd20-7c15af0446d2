import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["chatWidget"]

  connect() {
    this.initializePageTracking()
    this.setupChatWidget()
  }

  openChat() {
    // In production, this would integrate with your actual chat system
    // For now, show a placeholder widget
    if (this.hasChatWidgetTarget) {
      this.chatWidgetTarget.classList.remove('hidden')
    } else {
      // Fallback: integrate with your actual chat system
      this.integrateWithChatSystem()
    }
    
    this.trackEvent('chat_opened', { source: 'contact_page' })
  }

  closeChat() {
    if (this.hasChatWidgetTarget) {
      this.chatWidgetTarget.classList.add('hidden')
    }
    
    this.trackEvent('chat_closed', { source: 'contact_page' })
  }

  scheduleDemo() {
    // In production, integrate with your booking system (Calendly, etc.)
    // For now, show alert - replace with your actual booking flow
    
    this.trackEvent('demo_schedule_clicked', { source: 'contact_page' })
    
    // Example integration with Calendly
    if (typeof Calendly !== 'undefined') {
      Calendly.initPopupWidget({
        url: 'https://calendly.com/your-team/demo'
      })
    } else {
      // Fallback or redirect to booking page
      alert('Demo booking would open here. In production, this would integrate with your booking system.')
    }
  }

  setupChatWidget() {
    // Initialize actual chat widget if available
    // Examples: Intercom, Zendesk Chat, etc.
    
    if (typeof window.Intercom !== 'undefined') {
      window.Intercom('boot', {
        app_id: 'your_app_id'
      })
    }
    
    // Or Zendesk Chat
    if (typeof window.$zopim !== 'undefined') {
      window.$zopim.livechat.theme.setColor('#6366f1')
    }
  }

  integrateWithChatSystem() {
    // Production chat integration
    if (typeof window.Intercom !== 'undefined') {
      window.Intercom('show')
    } else if (typeof window.$zopim !== 'undefined') {
      window.$zopim.livechat.window.show()
    } else {
      // Custom chat solution
      console.log('Opening chat widget...')
    }
  }

  initializePageTracking() {
    // Track page view
    this.trackEvent('contact_page_view', {
      referrer: document.referrer,
      utm_source: new URLSearchParams(window.location.search).get('utm_source'),
      utm_medium: new URLSearchParams(window.location.search).get('utm_medium')
    })

    // Track scroll depth
    this.trackScrollDepth()
  }

  trackScrollDepth() {
    let maxScroll = 0
    const milestones = [25, 50, 75, 90]
    const tracked = new Set()

    window.addEventListener('scroll', () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      )
      
      maxScroll = Math.max(maxScroll, scrollPercent)
      
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !tracked.has(milestone)) {
          tracked.add(milestone)
          this.trackEvent('scroll_depth', { 
            depth: milestone,
            page: 'contact'
          })
        }
      })
    }, { passive: true })
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'contact_page',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        page: 'contact',
        ...properties
      })
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('Contact Page Event:', eventName, properties)
    }
  }
}