import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["chatWidget", "mapContainer", "mapToggle"]

  connect() {
    this.initializePageTracking()
    this.setupContactOptionsTracking()
    this.initializeChatWidget()
  }

  // Live Chat Functions
  openLiveChat() {
    if (this.hasChatWidgetTarget) {
      this.chatWidgetTarget.classList.remove('hidden')
      this.trackEvent('live_chat_opened', { source: 'hero_section' })
      
      // Animate in
      this.chatWidgetTarget.style.transform = 'scale(0.9) translateY(20px)'
      this.chatWidgetTarget.style.opacity = '0'
      
      setTimeout(() => {
        this.chatWidgetTarget.style.transform = 'scale(1) translateY(0)'
        this.chatWidgetTarget.style.opacity = '1'
        this.chatWidgetTarget.style.transition = 'all 0.3s ease-out'
      }, 10)
    } else {
      this.integrateWithChatSystem()
    }
  }

  closeLiveChat() {
    if (this.hasChatWidgetTarget) {
      this.chatWidgetTarget.style.transform = 'scale(0.9) translateY(20px)'
      this.chatWidgetTarget.style.opacity = '0'
      
      setTimeout(() => {
        this.chatWidgetTarget.classList.add('hidden')
      }, 300)
      
      this.trackEvent('live_chat_closed')
    }
  }

  // Demo Scheduling
  scheduleDemo() {
    this.trackEvent('demo_schedule_clicked', { source: 'hero_section' })
    
    // Check if Calendly is available
    if (typeof window.Calendly !== 'undefined') {
      window.Calendly.initPopupWidget({
        url: 'https://calendly.com/marketingai/demo',
        prefill: {
          name: this.getFormValue('name'),
          email: this.getFormValue('email'),
          customAnswers: {
            a1: this.getFormValue('company') || 'Not specified'
          }
        },
        utm: {
          utmCampaign: 'contact_page',
          utmSource: 'website',
          utmMedium: 'demo_button'
        }
      })
    } else {
      // Fallback: redirect to external booking page
      window.open('https://calendly.com/marketingai/demo', '_blank')
    }
  }

  // Google Maps Integration
  toggleMapView() {
    const container = this.mapContainerTarget
    const toggle = this.mapToggleTarget
    
    if (container.classList.contains('h-80')) {
      // Expand map
      container.classList.remove('h-80')
      container.classList.add('h-96')
      toggle.innerHTML = '<i class="fas fa-compress-arrows-alt mr-1"></i>Collapse'
      this.trackEvent('map_expanded')
    } else {
      // Collapse map
      container.classList.remove('h-96')
      container.classList.add('h-80')
      toggle.innerHTML = '<i class="fas fa-expand-arrows-alt mr-1"></i>Expand'
      this.trackEvent('map_collapsed')
    }
  }

  // Support Functions
  initializeChatWidget() {
    // Initialize actual chat widget if available
    if (typeof window.Intercom !== 'undefined') {
      window.Intercom('boot', {
        app_id: window.INTERCOM_APP_ID,
        custom_launcher_selector: '[data-action*="openLiveChat"]'
      })
    } else if (typeof window.$zopim !== 'undefined') {
      window.$zopim.livechat.theme.setColor('#6366f1')
      window.$zopim.livechat.window.setTitle('MarketingAI Support')
    }
  }

  integrateWithChatSystem() {
    // Production chat integration
    if (typeof window.Intercom !== 'undefined') {
      window.Intercom('show')
    } else if (typeof window.$zopim !== 'undefined') {
      window.$zopim.livechat.window.show()
    } else if (typeof window.HubSpotConversations !== 'undefined') {
      window.HubSpotConversations.widget.open()
    } else {
      // Custom fallback
      this.openLiveChat()
    }
  }

  setupContactOptionsTracking() {
    // Track when users hover over contact options
    const contactOptions = document.querySelectorAll('[data-action*="contact-page"]')
    contactOptions.forEach(option => {
      option.addEventListener('mouseenter', () => {
        const optionType = this.getContactOptionType(option)
        this.trackEvent('contact_option_hover', { option: optionType })
      })
    })
  }

  getContactOptionType(element) {
    if (element.textContent.includes('Live Chat')) return 'live_chat'
    if (element.textContent.includes('Email')) return 'email'
    if (element.textContent.includes('Phone')) return 'phone'
    if (element.textContent.includes('Demo')) return 'demo'
    return 'unknown'
  }

  getFormValue(fieldName) {
    const field = document.querySelector(`[name="${fieldName}"]`)
    return field ? field.value : ''
  }

  initializePageTracking() {
    this.trackEvent('contact_page_view', {
      referrer: document.referrer,
      utm_source: new URLSearchParams(window.location.search).get('utm_source'),
      utm_medium: new URLSearchParams(window.location.search).get('utm_medium'),
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`
    })

    this.trackScrollDepth()
    this.trackTimeOnPage()
    this.trackFormInteractions()
  }

  trackScrollDepth() {
    let maxScroll = 0
    const milestones = [25, 50, 75, 90]
    const tracked = new Set()

    const scrollHandler = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      )
      
      maxScroll = Math.max(maxScroll, scrollPercent)
      
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !tracked.has(milestone)) {
          tracked.add(milestone)
          this.trackEvent('scroll_depth', { 
            depth: milestone,
            page: 'contact'
          })
        }
      })
    }

    window.addEventListener('scroll', scrollHandler, { passive: true })
  }

  trackTimeOnPage() {
    this.startTime = Date.now()
    
    // Track time on page when user leaves
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Math.round((Date.now() - this.startTime) / 1000)
      this.trackEvent('time_on_page', {
        duration: timeOnPage,
        page: 'contact'
      })
    })
  }

  trackFormInteractions() {
    const formElements = document.querySelectorAll('input, textarea, select')
    formElements.forEach(element => {
      element.addEventListener('focus', () => {
        this.trackEvent('form_field_focus', { 
          field: element.name || element.id || 'unknown'
        })
      }, { once: true })
    })
  }

  trackEvent(eventName, properties = {}) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, {
        event_category: 'contact_page',
        page_location: window.location.href,
        page_title: document.title,
        ...properties
      })
    }

    // Mixpanel
    if (typeof mixpanel !== 'undefined') {
      mixpanel.track(eventName, {
        page: 'contact',
        ...properties
      })
    }

    // Custom analytics
    if (window.analytics && typeof window.analytics.track === 'function') {
      window.analytics.track(eventName, {
        page: 'contact',
        ...properties
      })
    }

    // HubSpot tracking
    if (typeof window._hsq !== 'undefined') {
      window._hsq.push(['trackEvent', {
        id: eventName,
        properties: properties
      }])
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Contact Page Event:', eventName, properties)
    }
  }
}