import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["email", "submit"]

  connect() {
    this.submitTarget.disabled = false
  }

  subscribe(event) {
    event.preventDefault()
    
    if (!this.validateEmail()) {
      return
    }

    this.setLoadingState(true)
    
    const formData = new FormData(event.target)
    
    fetch(event.target.action, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: formData.get('email'),
        source: 'footer_newsletter'
      })
    })
    .then(response => response.json())
    .then(data => {
      this.handleSuccess(data)
    })
    .catch(error => {
      this.handleError(error)
    })
    .finally(() => {
      this.setLoadingState(false)
    })
  }

  validateEmail() {
    const email = this.emailTarget.value.trim()
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    
    if (!email || !emailRegex.test(email)) {
      this.showError("Please enter a valid email address")
      this.emailTarget.focus()
      return false
    }
    
    this.clearError()
    return true
  }

  setLoadingState(loading) {
    this.submitTarget.disabled = loading
    
    if (loading) {
      this.submitTarget.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Subscribing...'
    } else {
      this.submitTarget.innerHTML = 'Subscribe'
    }
  }

  handleSuccess(data) {
    this.showSuccess("Successfully subscribed! Check your email for confirmation.")
    this.emailTarget.value = ""
  }

  handleError(error) {
    this.showError("Something went wrong. Please try again.")
    console.error('Newsletter subscription error:', error)
  }

  showSuccess(message) {
    this.showMessage(message, 'success')
  }

  showError(message) {
    this.showMessage(message, 'error')
  }

  showMessage(message, type) {
    // Remove existing messages
    this.clearError()
    
    const messageDiv = document.createElement('div')
    messageDiv.className = `mt-2 p-3 rounded-lg text-sm ${
      type === 'success' 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
    }`
    messageDiv.textContent = message
    messageDiv.setAttribute('role', 'alert')
    messageDiv.setAttribute('aria-live', 'polite')
    
    this.emailTarget.parentNode.appendChild(messageDiv)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv)
      }
    }, 5000)
  }

  clearError() {
    const existingMessage = this.emailTarget.parentNode.querySelector('[role="alert"]')
    if (existingMessage) {
      existingMessage.parentNode.removeChild(existingMessage)
    }
  }
}

