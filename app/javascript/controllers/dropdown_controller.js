import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["trigger", "menu"]

  connect() {
    this.close = this.close.bind(this)
  }

  disconnect() {
    document.removeEventListener("click", this.close)
    document.removeEventListener("keydown", this.handleKeydown)
  }

  toggle(event) {
    event.stopPropagation()
    
    if (this.menuTarget.classList.contains("hidden")) {
      this.open()
    } else {
      this.close()
    }
  }

  open() {
    this.menuTarget.classList.remove("hidden")
    this.triggerTarget.setAttribute("aria-expanded", "true")
    
    // Add event listeners for outside clicks and keyboard navigation
    setTimeout(() => {
      document.addEventListener("click", this.close)
      document.addEventListener("keydown", this.handleKeydown.bind(this))
    }, 0)
    
    // Focus first menu item
    const firstItem = this.menuTarget.querySelector("a")
    if (firstItem) {
      firstItem.focus()
    }
  }

  close() {
    this.menuTarget.classList.add("hidden")
    this.triggerTarget.setAttribute("aria-expanded", "false")
    
    document.removeEventListener("click", this.close)
    document.removeEventListener("keydown", this.handleKeydown)
  }

  handleKeydown(event) {
    if (event.key === "Escape") {
      this.close()
      this.triggerTarget.focus()
      return
    }

    if (event.key === "ArrowDown" || event.key === "ArrowUp") {
      event.preventDefault()
      this.navigateMenu(event.key === "ArrowDown" ? 1 : -1)
    }
  }

  navigateMenu(direction) {
    const menuItems = Array.from(this.menuTarget.querySelectorAll("a"))
    const currentIndex = menuItems.indexOf(document.activeElement)
    
    let nextIndex = currentIndex + direction
    
    if (nextIndex < 0) {
      nextIndex = menuItems.length - 1
    } else if (nextIndex >= menuItems.length) {
      nextIndex = 0
    }
    
    menuItems[nextIndex].focus()
  }
}
