import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { speed: Number }

  connect() {
    this.speed = this.speedValue || 0.5
    this.initialOffset = this.element.offsetTop
    
    this.handleScroll = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.handleScroll, { passive: true })
    
    // Set initial transform
    this.handleScroll()
  }

  disconnect() {
    window.removeEventListener('scroll', this.handleScroll)
  }

  handleScroll() {
    const scrolled = window.pageYOffset
    const parallax = scrolled * this.speed
    
    this.element.style.transform = `translateY(${parallax}px)`
  }
}