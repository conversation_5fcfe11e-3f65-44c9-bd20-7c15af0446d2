import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { delay: Number }

  connect() {
    this.element.style.opacity = '0'
    this.element.style.transform = 'translateY(30px)'
    this.element.style.transition = 'opacity 0.8s ease-out, transform 0.8s ease-out'
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateIn()
          this.observer.unobserve(this.element)
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    })

    // Apply delay if specified
    const delay = this.delayValue || 0
    setTimeout(() => {
      this.observer.observe(this.element)
    }, delay)
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  animateIn() {
    this.element.style.opacity = '1'
    this.element.style.transform = 'translateY(0)'
  }
}
