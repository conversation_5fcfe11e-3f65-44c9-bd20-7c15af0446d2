class ContactMessageJob < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(contact_message_id)
    @contact_message = ContactMessage.find(contact_message_id)

    send_confirmation_email
    send_internal_notification
    track_contact_submission
    update_crm_if_configured

  rescue ActiveRecord::RecordNotFound
    Rails.logger.error "Contact message not found: #{contact_message_id}"
  rescue => e
    Rails.logger.error "Contact message job failed: #{e.message}"
    raise
  end

  private

  def send_confirmation_email
    ContactMailer.confirmation_email(@contact_message).deliver_now
  end

  def send_internal_notification
    # Send to appropriate team based on subject
    case @contact_message.subject
    when "sales"
      ContactMailer.sales_notification(@contact_message).deliver_now
    when "support", "bug"
      ContactMailer.support_notification(@contact_message).deliver_now
    when "partnership"
      ContactMailer.partnership_notification(@contact_message).deliver_now
    when "press"
      ContactMailer.press_notification(@contact_message).deliver_now
    else
      ContactMailer.general_notification(@contact_message).deliver_now
    end

    # Also send to Slack if configured
    send_slack_notification if ENV["SLACK_WEBHOOK_URL"].present?
  end

  def send_slack_notification
    SlackNotificationService.send_contact_message(@contact_message)
  rescue => e
    Rails.logger.error "Slack notification failed: #{e.message}"
  end

  def track_contact_submission
    AnalyticsService.track_event(
      event: "contact_form_submission",
      properties: {
        subject: @contact_message.subject,
        has_company: @contact_message.company.present?,
        utm_source: @contact_message.utm_source,
        utm_medium: @contact_message.utm_medium,
        utm_campaign: @contact_message.utm_campaign
      }
    )
  end

  def update_crm_if_configured
    # Update CRM (Salesforce, HubSpot, etc.) if configured
    return unless Rails.env.production?

    CrmIntegrationService.create_lead(
      name: @contact_message.name,
      email: @contact_message.email,
      company: @contact_message.company,
      message: @contact_message.message,
      source: "contact_form",
      utm_params: {
        source: @contact_message.utm_source,
        medium: @contact_message.utm_medium,
        campaign: @contact_message.utm_campaign
      }
    )
  rescue => e
    Rails.logger.error "CRM integration failed: #{e.message}"
  end
end
