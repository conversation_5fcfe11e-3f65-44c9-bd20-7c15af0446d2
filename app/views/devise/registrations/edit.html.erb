<%= content_for :title, "Account Settings" %>
<div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-4xl mx-auto">
    <!-- Header Section -->
    <div class="text-center mb-8">
      <div class="flex justify-center">
        <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-4">
          <i class="fas fa-user-cog text-white text-2xl"></i>
        </div>
      </div>
      <h1 class="text-3xl font-bold text-gray-900">Account Settings</h1>
      <p class="mt-2 text-gray-600">Manage your account information and preferences</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Form -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
          <!-- Email Confirmation Notice -->
          <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-800">
                    <strong>Email confirmation pending:</strong> We've sent a confirmation email to 
                    <span class="font-medium"><%= resource.unconfirmed_email %></span>
                  </p>
                </div>
              </div>
            </div>
          <% end %>

          <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
                        html: { method: :put, class: "p-8 space-y-8" }) do |f| %>

            <!-- Error Messages -->
            <% if resource.errors.any? %>
              <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      <%= pluralize(resource.errors.count, "error") %> prohibited your account from being updated:
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                      <ul class="list-disc list-inside space-y-1">
                        <% resource.errors.full_messages.each do |message| %>
                          <li><%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <!-- Personal Information Section -->
            <div>
              <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-user text-primary mr-3"></i>
                Personal Information
              </h2>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                  <%= f.label :first_name, "First name", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-user text-gray-400 text-sm"></i>
                    </div>
                    <%= f.text_field :first_name,
                        placeholder: "John",
                        class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                  </div>
                </div>

                <!-- Last Name -->
                <div>
                  <%= f.label :last_name, "Last name", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-user text-gray-400 text-sm"></i>
                    </div>
                    <%= f.text_field :last_name,
                        placeholder: "Doe",
                        class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                  </div>
                </div>
              </div>

              <!-- Email -->
              <div class="mt-6">
                <%= f.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                  </div>
                  <%= f.email_field :email,
                      autofocus: true,
                      autocomplete: "email",
                      placeholder: "<EMAIL>",
                      class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                </div>
                <p class="mt-1 text-xs text-gray-500">
                  <i class="fas fa-info-circle mr-1"></i>
                  You'll receive a confirmation email if you change your email address
                </p>
              </div>
            </div>

            <!-- Company Information Section -->
            <div class="border-t border-gray-200 pt-8">
              <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-building text-primary mr-3"></i>
                Company Information
              </h2>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Company Name -->
                <div>
                  <%= f.label :company, "Company name", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-building text-gray-400"></i>
                    </div>
                    <%= f.text_field :company,
                        placeholder: "Your company name",
                        class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                  </div>
                </div>

                <!-- Business Size -->
                <div>
                  <%= f.label :business_size, "Business size", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-users text-gray-400"></i>
                    </div>
                    <%= f.select :business_size,
                        options_for_select([
                          ["Select your business size", ""],
                          ["1-10 employees", "1-10"],
                          ["11-50 employees", "11-50"],
                          ["51-200 employees", "51-200"],
                          ["201-500 employees", "201-500"],
                          ["500+ employees", "500+"]
                        ], resource.business_size),
                        {},
                        {
                          class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors appearance-none bg-white"
                        } %>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Password Section -->
            <div class="border-t border-gray-200 pt-8">
              <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-lock text-primary mr-3"></i>
                Security Settings
              </h2>

              <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-blue-700">
                      Leave password fields blank if you don't want to change your password. 
                      <% if @minimum_password_length %>
                        Passwords must be at least <%= @minimum_password_length %> characters long.
                      <% end %>
                    </p>
                  </div>
                </div>
              </div>

              <div class="space-y-6">
                <!-- New Password -->
                <div>
                  <%= f.label :password, "New password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative" data-controller="password-strength">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <%= f.password_field :password,
                          autocomplete: "new-password",
                          placeholder: "Enter new password (optional)",
                          data: { 
                            action: "input->password-strength#checkStrength",
                            password_strength_target: "input"
                          },
                          class: "block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button type="button" 
                              data-controller="password-toggle"
                              data-action="click->password-toggle#toggle"
                              class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-eye" data-password-toggle-target="icon"></i>
                      </button>
                    </div>
                  </div>
                  <div class="mt-2" data-password-strength-target="container" style="display: none;">
                    <div class="flex space-x-1" data-password-strength-target="bars">
                      <div class="h-1 w-1/4 bg-red-300 rounded" data-password-strength-target="bar"></div>
                      <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
                      <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
                      <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1" data-password-strength-target="text">
                      Password strength: <span data-password-strength-target="strength">Weak</span>
                    </p>
                  </div>
                </div>

                <!-- Confirm Password -->
                <div>
                  <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <%= f.password_field :password_confirmation,
                          autocomplete: "new-password",
                          placeholder: "Confirm new password",
                          class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                  </div>
                </div>

                <!-- Current Password -->
                <div>
                  <%= f.label :current_password, "Current password", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-key text-gray-400"></i>
                    </div>
                    <%= f.password_field :current_password,
                          autocomplete: "current-password",
                          required: true,
                          placeholder: "Enter your current password",
                          class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
                  </div>
                  <p class="mt-1 text-xs text-gray-500">
                    <i class="fas fa-shield-alt mr-1"></i>
                    Required to confirm any changes to your account
                  </p>
                </div>
              </div>
            </div>

            <!-- Preferences Section -->
            <div class="border-t border-gray-200 pt-8">
              <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-cog text-primary mr-3"></i>
                Preferences
              </h2>

              <div class="space-y-4">
                <!-- Marketing Emails -->
                <div class="flex items-start">
                  <div class="flex items-center h-5 mt-1">
                    <%= f.check_box :marketing_emails,
                        class: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" %>
                  </div>
                  <div class="ml-3">
                    <%= f.label :marketing_emails, class: "text-sm text-gray-700" do %>
                      <span class="font-medium">Marketing communications</span>
                      <p class="text-gray-500 mt-1">Receive emails about new features, tips, and marketing insights</p>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="border-t border-gray-200 pt-8">
              <div class="flex justify-between items-center">
                <div class="flex space-x-4">
                  <%= f.submit "Save Changes",
                        class: "inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors" do %>
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                  <% end %>
                  
                  <%= link_to "Cancel", :back,
                        class: "inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors" do %>
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Account Overview -->
        <div class="bg-white rounded-2xl shadow-xl p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-chart-pie text-primary mr-2"></i>
            Account Overview
          </h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Member since</span>
              <span class="text-sm font-medium text-gray-900">
                <%= resource.created_at&.strftime("%B %Y") || "N/A" %>
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Account status</span>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <i class="fas fa-check-circle mr-1"></i>
                Active
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">Email verified</span>
              <% if resource.confirmed_at %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <i class="fas fa-shield-check mr-1"></i>
                  Verified
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <i class="fas fa-exclamation-triangle mr-1"></i>
                  Pending
                </span>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-2xl shadow-xl p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-bolt text-primary mr-2"></i>
            Quick Actions
          </h3>
          <div class="space-y-3">
            <%= link_to root_path,
                  class: "flex items-center p-3 text-sm text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" do %>
              <i class="fas fa-home text-gray-400 mr-3"></i>
              Go to Dashboard
            <% end %>
            
            <%= link_to new_user_session_path,
                  class: "flex items-center p-3 text-sm text-gray-700 rounded-lg hover:bg-gray-50 transition-colors" do %>
              <i class="fas fa-sign-out-alt text-gray-400 mr-3"></i>
              Sign Out
            <% end %>
          </div>
        </div>

        <!-- Support -->
        <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl shadow-xl p-6 text-white">
          <h3 class="text-lg font-semibold mb-2 flex items-center">
            <i class="fas fa-headset mr-2"></i>
            Need Help?
          </h3>
          <p class="text-blue-100 text-sm mb-4">
            Our support team is here to help you with any questions.
          </p>
          <a href="mailto:<EMAIL>" 
             class="inline-flex items-center text-sm font-medium text-white hover:text-blue-100 transition-colors">
            <i class="fas fa-envelope mr-2"></i>
            Contact Support
          </a>
        </div>

        <!-- Danger Zone -->
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div class="bg-red-50 px-6 py-4 border-b border-red-200">
            <h3 class="text-lg font-semibold text-red-900 flex items-center">
              <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
              Danger Zone
            </h3>
          </div>
          <div class="p-6">
            <p class="text-sm text-gray-600 mb-4">
              Once you delete your account, there is no going back. Please be certain.
            </p>
            <%= button_to "Delete Account", registration_path(resource_name),
                  method: :delete,
                  data: {
                    "turbo-confirm": "Are you absolutely sure? This action cannot be undone and will permanently delete your account and all associated data."
                  },
                  class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors" do %>
              <i class="fas fa-trash mr-2"></i>
              Delete Account
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
