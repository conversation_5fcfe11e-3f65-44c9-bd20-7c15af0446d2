<%= content_for :title, "Register" %>
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- <PERSON><PERSON> and <PERSON>er -->
    <div class="text-center">
      <div class="flex justify-center">
        <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-4">
          <i class="fas fa-chart-line text-white text-2xl"></i>
        </div>
      </div>
      <h2 class="text-3xl font-bold text-gray-900">Create your account</h2>
      <p class="mt-2 text-gray-600">Start your 14-day free trial today</p>
    </div>

    <!-- Signup Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with model: resource, as: resource_name, url: registration_path(resource_name), local: true, class: "space-y-6", data: { turbo: false } do |form| %>
        
        <% if resource.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(resource.errors.count, "error") %> prohibited this account from being created:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc list-inside space-y-1">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <%= form.label :first_name, "First name", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :first_name, 
                placeholder: "John",
                required: true,
                class: "block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
          <div>
            <%= form.label :last_name, "Last name", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :last_name,
                placeholder: "Doe", 
                required: true,
                class: "block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
        </div>

        <div>
          <%= form.label :company_name, "Company name", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-building text-gray-400"></i>
            </div>
            <%= form.text_field :company_name,
                placeholder: "Your company name",
                required: true,
                class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
        </div>

        <div>
          <%= form.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-envelope text-gray-400"></i>
            </div>
            <%= form.email_field :email,
                placeholder: "<EMAIL>",
                required: true,
                class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
        </div>

        <div>
          <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative" data-controller="password-strength">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-gray-400"></i>
            </div>
            <%= form.password_field :password,
                placeholder: "Create a strong password",
                required: true,
                data: { 
                  action: "input->password-strength#checkStrength",
                  password_strength_target: "input"
                },
                class: "block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button type="button" 
                      data-controller="password-toggle"
                      data-action="click->password-toggle#toggle"
                      class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-eye" data-password-toggle-target="icon"></i>
              </button>
            </div>
          </div>
          <div class="mt-2">
            <div class="flex space-x-1" data-password-strength-target="bars">
              <div class="h-1 w-1/4 bg-red-300 rounded" data-password-strength-target="bar"></div>
              <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
              <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
              <div class="h-1 w-1/4 bg-gray-200 rounded" data-password-strength-target="bar"></div>
            </div>
            <p class="text-xs text-gray-500 mt-1" data-password-strength-target="text">
              Password strength: <span data-password-strength-target="strength">Weak</span>
            </p>
          </div>
        </div>

        <div>
          <%= form.label :password_confirmation, "Confirm password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-gray-400"></i>
            </div>
            <%= form.password_field :password_confirmation,
                placeholder: "Confirm your password",
                required: true,
                class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
        </div>

        <div>
          <%= form.label :business_size, "Business size", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :business_size,
              options_for_select([
                ["Select your business size", ""],
                ["1-10 employees", "1-10"],
                ["11-50 employees", "11-50"],
                ["51-200 employees", "51-200"],
                ["201-500 employees", "201-500"],
                ["500+ employees", "500+"]
              ]),
              {},
              {
                required: true,
                class: "block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
              } %>
        </div>

        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :terms_accepted,
                required: true,
                class: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :terms_accepted, class: "text-gray-700" do %>
              I agree to the 
              <%= link_to "Terms of Service", terms_path, class: "text-primary hover:text-blue-700 font-medium" %> 
              and 
              <%= link_to "Privacy Policy", privacy_path, class: "text-primary hover:text-blue-700 font-medium" %>
            <% end %>
          </div>
        </div>

        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :marketing_emails,
                class: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :marketing_emails, "I'd like to receive marketing emails about new features and tips", 
                class: "text-gray-700" %>
          </div>
        </div>

        <div>
          <%= form.submit "Start your free trial",
              class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors" do %>
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <i class="fas fa-user-plus text-blue-300 group-hover:text-blue-200"></i>
            </span>
            Start your free trial
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Navigation Links -->
    <%= render "devise/shared/links" %>

    <!-- Trial Benefits -->
    <div class="bg-white rounded-xl p-6 shadow-lg">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">Your 14-day trial includes:</h3>
      <div class="space-y-3">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-envelope text-accent text-sm"></i>
          </div>
          <span class="text-sm text-gray-700">Unlimited email campaigns</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-users text-primary text-sm"></i>
          </div>
          <span class="text-sm text-gray-700">Up to 10,000 contacts</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-robot text-secondary text-sm"></i>
          </div>
          <span class="text-sm text-gray-700">AI-powered automation</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-chart-bar text-yellow-600 text-sm"></i>
          </div>
          <span class="text-sm text-gray-700">Advanced analytics</span>
        </div>
      </div>
    </div>
  </div>
</div>