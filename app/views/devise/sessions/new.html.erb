<%= content_for :title, "Login" %>
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- <PERSON><PERSON> and <PERSON>er -->
    <div class="text-center">
      <div class="flex justify-center">
        <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-4">
          <i class="fas fa-chart-line text-white text-2xl"></i>
        </div>
      </div>
      <h2 class="text-3xl font-bold text-gray-900">Welcome back</h2>
      <p class="mt-2 text-gray-600">Sign in to your account</p>
    </div>

    <!-- Login Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>                
        <% if alert %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800"><%= alert %></p>
              </div>
            </div>
          </div>
        <% end %>

        <% if notice %>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-check-circle text-green-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-green-800"><%= notice %></p>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= f.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-envelope text-gray-400"></i>
            </div>
            <%= f.email_field :email,
                placeholder: "Enter your email",
                required: true,
                autofocus: true,
                autocomplete: "email",
                class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
        </div>

        <div>
          <%= f.label :password, "Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-gray-400"></i>
            </div>
            <%= f.password_field :password,
                placeholder: "Enter your password",
                required: true,
                autocomplete: "current-password",
                class: "block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button type="button" 
                      data-controller="password-toggle"
                      data-action="click->password-toggle#toggle"
                      class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-eye" data-password-toggle-target="icon"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <% if devise_mapping.rememberable? %>
              <%= f.check_box :remember_me, class: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" %>
              <%= f.label :remember_me, "Remember me", class: "ml-2 block text-sm text-gray-700" %>
            <% end %>
          </div>

          <div class="text-sm">
            <%= link_to "Forgot your password?", new_password_path(resource_name), 
                class: "font-medium text-primary hover:text-blue-700 transition-colors" %>
          </div>
        </div>

        <div>
          <%= f.submit "Sign in",
              class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors" do %>
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <i class="fas fa-sign-in-alt text-blue-300 group-hover:text-blue-200"></i>
            </span>
            Sign in
          <% end %>
        </div>

        <!-- Social Login -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <div class="mt-6 grid grid-cols-2 gap-3">
            <%= link_to user_google_oauth2_omniauth_authorize_path, method: :post,
                class: "w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors" do %>
              <i class="fab fa-google text-red-500"></i>
              <span class="ml-2">Google</span>
            <% end %>
            
            <%= link_to user_microsoft_graph_omniauth_authorize_path, method: :post,
                class: "w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors" do %>
              <i class="fab fa-microsoft text-blue-500"></i>
              <span class="ml-2">Microsoft</span>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Signup link -->
    <div class="text-center">
      <p class="text-sm text-gray-600">
        Don't have an account?
        <%= link_to "Start your free trial", new_user_registration_path, 
            class: "font-medium text-primary hover:text-blue-700 transition-colors" %>
      </p>
    </div>

    <!-- Features Summary -->
    <div class="bg-white rounded-xl p-6 shadow-lg">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">Why choose our platform?</h3>
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-robot text-primary text-lg"></i>
          </div>
          <p class="text-xs text-gray-700 font-medium">AI-Powered</p>
          <p class="text-xs text-gray-500">Automation</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-clock text-accent text-lg"></i>
          </div>
          <p class="text-xs text-gray-700 font-medium">Save Time</p>
          <p class="text-xs text-gray-500">80% faster</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-chart-line text-secondary text-lg"></i>
          </div>
          <p class="text-xs text-gray-700 font-medium">Better ROI</p>
          <p class="text-xs text-gray-500">3x improvement</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-shield-alt text-yellow-600 text-lg"></i>
          </div>
          <p class="text-xs text-gray-700 font-medium">Enterprise</p>
          <p class="text-xs text-gray-500">Security</p>
        </div>
      </div>
    </div>
  </div>
</div>
