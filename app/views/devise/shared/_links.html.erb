<div class="space-y-4">
  <!-- Authentication Links Section -->
  <% if controller_name != 'sessions' || controller_name != 'registrations' || controller_name != 'passwords' %>
    <div class="text-center space-y-3">
      
      <%- if controller_name != 'sessions' %>
        <div>
          <p class="text-sm text-gray-600">
            Already have an account?
            <%= link_to "Sign in", new_session_path(resource_name), 
                class: "font-medium text-primary hover:text-blue-700 transition-colors" %>
          </p>
        </div>
      <% end %>

      <%- if devise_mapping.registerable? && controller_name != 'registrations' %>
        <div>
          <p class="text-sm text-gray-600">
            Don't have an account?
            <%= link_to "Start your free trial", new_registration_path(resource_name), 
                class: "font-medium text-primary hover:text-blue-700 transition-colors" %>
          </p>
        </div>
      <% end %>

      <%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
        <div>
          <p class="text-sm text-gray-600">
            Trouble signing in?
            <%= link_to "Reset your password", new_password_path(resource_name), 
                class: "font-medium text-primary hover:text-blue-700 transition-colors" %>
          </p>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Additional Support Links -->
  <% if devise_mapping.confirmable? && controller_name != 'confirmations' %>
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fas fa-envelope text-yellow-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-yellow-800">
            Didn't receive confirmation instructions?
            <%= link_to "Resend confirmation", new_confirmation_path(resource_name), 
                class: "font-medium text-yellow-900 hover:text-yellow-700 underline ml-1" %>
          </p>
        </div>
      </div>
    </div>
  <% end %>

  <%- if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fas fa-lock text-red-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800">
            Account locked?
            <%= link_to "Get unlock instructions", new_unlock_path(resource_name), 
                class: "font-medium text-red-900 hover:text-red-700 underline ml-1" %>
          </p>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Social Authentication -->
  <%- if devise_mapping.omniauthable? && resource_class.omniauth_providers.any? %>
    <div class="space-y-4">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">Or continue with</span>
        </div>
      </div>

      <div class="grid gap-3 <%= resource_class.omniauth_providers.count > 2 ? 'grid-cols-1' : 'grid-cols-2' %>">
        <%- resource_class.omniauth_providers.each do |provider| %>
          <% provider_name = OmniAuth::Utils.camelize(provider) %>
          <% provider_class = case provider.to_s
                              when 'google_oauth2'
                                'fab fa-google text-red-500'
                              when 'facebook'
                                'fab fa-facebook text-blue-600'
                              when 'twitter'
                                'fab fa-twitter text-blue-400'
                              when 'github'
                                'fab fa-github text-gray-800'
                              when 'linkedin'
                                'fab fa-linkedin text-blue-700'
                              when 'microsoft_graph', 'microsoft_oauth2', 'azure_oauth2'
                                'fab fa-microsoft text-blue-500'
                              when 'apple'
                                'fab fa-apple text-gray-800'
                              else
                                'fas fa-sign-in-alt text-gray-500'
                              end %>
          
          <%= button_to send("#{resource_name}_#{provider}_omniauth_authorize_path"), 
              method: :post,
              data: { turbo: false },
              class: "w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors" do %>
            <i class="<%= provider_class %> mr-2"></i>
            <span><%= provider_name %></span>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Help and Support -->
  <% unless controller_name == 'passwords' %>
    <div class="bg-gray-50 rounded-lg p-4 text-center">
      <p class="text-xs text-gray-600 mb-2">Need help?</p>
      <div class="flex justify-center space-x-4 text-xs">
        <a href="mailto:<EMAIL>" class="text-primary hover:text-blue-700 flex items-center">
          <i class="fas fa-envelope mr-1"></i>
          Support
        </a>
        <%= link_to "#", class: "text-primary hover:text-blue-700 flex items-center" do %>
          <i class="fas fa-question-circle mr-1"></i>
          Help Center
        <% end %>
        <%= link_to "#", class: "text-primary hover:text-blue-700 flex items-center" do %>
          <i class="fas fa-comments mr-1"></i>
          Live Chat
        <% end %>
      </div>
    </div>
  <% end %>
</div>