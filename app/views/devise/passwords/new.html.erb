<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- <PERSON><PERSON> and Header -->
    <div class="text-center">
      <div class="flex justify-center">
        <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-4">
          <i class="fas fa-chart-line text-white text-2xl"></i>
        </div>
      </div>
      <h2 class="text-3xl font-bold text-gray-900">Forgot your password?</h2>
      <p class="mt-2 text-gray-600">No worries, we'll send you reset instructions</p>
    </div>

    <!-- Forgot Password Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with model: resource, as: resource_name, url: password_path(resource_name), method: :post, local: true, class: "space-y-6", data: { turbo: false } do |form| %>
        
        <% if alert %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800"><%= alert %></p>
              </div>
            </div>
          </div>
        <% end %>

        <% if notice %>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-check-circle text-green-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-green-800"><%= notice %></p>
              </div>
            </div>
          </div>
        <% end %>

        <% if resource.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(resource.errors.count, "error") %> prohibited this request:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc list-inside space-y-1">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= form.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-envelope text-gray-400"></i>
            </div>
            <%= form.email_field :email,
                placeholder: "Enter your email address",
                required: true,
                autofocus: true,
                autocomplete: "email",
                value: params[:email],
                class: "block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors" %>
          </div>
          <p class="mt-2 text-sm text-gray-500">
            We'll send password reset instructions to this email address.
          </p>
        </div>

        <div>
          <%= form.submit "Send reset instructions",
              class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors" do %>
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <i class="fas fa-paper-plane text-blue-300 group-hover:text-blue-200"></i>
            </span>
            Send reset instructions
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Navigation Links -->
    <%= render "devise/shared/links" %>

    <!-- Help Section -->
    <div class="bg-white rounded-xl p-6 shadow-lg">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">Need help?</h3>
      <div class="space-y-3">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
            <i class="fas fa-clock text-primary text-sm"></i>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">Check your email</p>
            <p class="text-xs text-gray-500">Instructions will arrive within 5 minutes</p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
            <i class="fas fa-folder text-yellow-600 text-sm"></i>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">Check spam folder</p>
            <p class="text-xs text-gray-500">Sometimes emails end up there</p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
            <i class="fas fa-headset text-accent text-sm"></i>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">Contact support</p>
            <p class="text-xs text-gray-500">
              Email us at 
              <a href="mailto:<EMAIL>" class="text-primary hover:text-blue-700">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Notice -->
    <div class="bg-blue-50 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fas fa-shield-alt text-blue-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-blue-800">
            <strong>Security Note:</strong> For your protection, password reset links expire after 24 hours and can only be used once.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>