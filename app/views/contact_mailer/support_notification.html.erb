<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>New Support Request</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #10b981; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
    .field { margin-bottom: 15px; }
    .label { font-weight: bold; color: #555; }
    .value { margin-top: 5px; }
    .message { background: white; padding: 15px; border-radius: 4px; border-left: 4px solid #10b981; }
    .priority { padding: 10px; border-radius: 4px; margin: 15px 0; }
    .priority.high { background: #fee2e2; border-left: 4px solid #ef4444; }
    .priority.normal { background: #f0f9ff; border-left: 4px solid #3b82f6; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2>🛠️ New Support Request</h2>
    </div>
    <div class="content">
      <div class="priority <%= @contact_message.subject == 'bug' ? 'high' : 'normal' %>">
        <strong>Priority:</strong> <%= @contact_message.subject == 'bug' ? 'HIGH' : 'Normal' %>
        | <strong>Response SLA:</strong> <%= @contact_message.estimated_response_time %>
      </div>

      <div class="field">
        <div class="label">Contact Information:</div>
        <div class="value">
          <strong><%= @contact_message.name %></strong><br>
          📧 <%= @contact_message.email %><br>
          <% if @contact_message.company.present? %>🏢 <%= @contact_message.company %><br><% end %>
        </div>
      </div>

      <div class="field">
        <div class="label">Issue Type:</div>
        <div class="value"><%= @contact_message.subject_label %></div>
      </div>

      <div class="field">
        <div class="label">Description:</div>
        <div class="message"><%= simple_format(@contact_message.message) %></div>
      </div>

      <div class="field">
        <div class="label">Technical Details:</div>
        <div class="value">
          IP: <%= @contact_message.ip_address %><br>
          User Agent: <%= truncate(@contact_message.user_agent, length: 100) %>
        </div>
      </div>

      <div class="field">
        <div class="label">Submitted:</div>
        <div class="value"><%= @contact_message.created_at.strftime("%B %d, %Y at %I:%M %p %Z") %></div>
      </div>
    </div>
  </div>
</body>
</html>