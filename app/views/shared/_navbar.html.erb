<% if user_signed_in? %>
<nav class="bg-white border-b border-gray-200 sticky top-0 z-50" 
     data-controller="navbar" 
     role="navigation" 
     aria-label="Main navigation">
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      
      <!-- Left side - Logo and Navigation -->
      <div class="flex items-center">
        <!-- Logo -->
        <%= link_to '#', 
            class: "flex items-center space-x-3 group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg",
            "aria-label": "MarketingAI Dashboard" do %>
          <div class="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
            <i class="fas fa-chart-line text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="hidden sm:block">
            <span class="text-xl font-bold text-gray-900">MarketingAI</span>
            <% if current_user&.tenant %>
              <div class="text-xs text-gray-500 -mt-1" title="Current tenant">
                <%= current_user.tenant.name.truncate(20) %>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center ml-10 space-x-1" role="menubar">
          <%= nav_link_to "Dashboard", dashboard_path, 
              icon: "fas fa-tachometer-alt",
              policy: :dashboard?,
              aria_label: "View dashboard" %>
          
          <% if policy(:campaign).index? %>
            <%= nav_link_to "Campaigns", campaigns_path, 
                icon: "fas fa-bullhorn",
                aria_label: "Manage campaigns" %>
          <% end %>
          
          <% if policy(:analytic).index? %>
            <%= nav_link_to "Analytics", analytics_path, 
                icon: "fas fa-chart-bar",
                aria_label: "View analytics" %>
          <% end %>
          
          <% if policy(:automation).index? %>
            <%= nav_link_to "Automation", automation_path, 
                icon: "fas fa-robot",
                aria_label: "Marketing automation" %>
          <% end %>
          
          <% if policy(:contact).index? %>
            <%= nav_link_to "Contacts", contacts_path, 
                icon: "fas fa-users",
                aria_label: "Manage contacts" %>
          <% end %>
          
          <% if policy(:content).index? %>
            <%= nav_link_to "Content", content_path, 
                icon: "fas fa-file-alt",
                aria_label: "Content management" %>
          <% end %>
          
          <% if current_user&.admin? || current_user&.owner? %>
            <%= nav_link_to "Settings", settings_path, 
                icon: "fas fa-cog",
                aria_label: "Application settings" %>
          <% end %>
        </div>
      </div>

      <!-- Right side - Search, Notifications, User Menu -->
      <div class="flex items-center space-x-4">
        
        <!-- Search Bar (Desktop) -->
        <% if policy(:search).perform? %>
          <div class="hidden lg:block">
            <div class="relative" data-controller="search">
              <label for="global-search" class="sr-only">Search campaigns, contacts, and content</label>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400" aria-hidden="true"></i>
              </div>
              <input type="text" 
                     id="global-search"
                     placeholder="Search campaigns, contacts..."
                     data-search-target="input"
                     data-action="input->search#query focus->search#show blur->search#hide"
                     class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
              
              <!-- Search Results Dropdown -->
              <div data-search-target="results" 
                   class="hidden absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                   role="listbox"
                   aria-label="Search results">
                <div class="p-2">
                  <div class="text-sm text-gray-500 p-2">Start typing to search...</div>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Notifications -->
        <% if policy(:notification).index? %>
          <div class="relative" data-controller="notifications">
            <button type="button" 
                    data-action="click->notifications#toggle"
                    class="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg transition-colors duration-200"
                    aria-label="<%= notifications_aria_label %>"
                    aria-expanded="false"
                    aria-haspopup="true">
              <i class="fas fa-bell text-lg" aria-hidden="true"></i>
              <% if user_unread_notifications.any? %>
                <span class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse"
                      aria-label="<%= pluralize(user_unread_notifications.count, 'unread notification') %>">
                  <%= user_unread_notifications.count %>
                </span>
              <% end %>
            </button>

            <!-- Notifications Dropdown -->
            <div data-notifications-target="dropdown"
                 class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                 role="menu"
                 aria-labelledby="notifications-menu">
              <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                  <% if user_unread_notifications.any? %>
                    <%= button_to "Mark all read", mark_all_read_notifications_path, 
                        method: :patch,
                        class: "text-sm text-primary hover:text-blue-700 bg-transparent border-0 p-0",
                        "aria-label": "Mark all notifications as read" %>
                  <% end %>
                </div>
              </div>
              
              <div class="max-h-96 overflow-y-auto">
                <% if user_notifications.any? %>
                  <% user_notifications.recent.limit(5).each do |notification| %>
                    <div class="p-4 border-b border-gray-100 hover:bg-gray-50 <%= 'bg-blue-50' unless notification.read? %> transition-colors"
                         role="menuitem">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                          <i class="<%= notification.icon %> text-primary" aria-hidden="true"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium text-gray-900"><%= notification.title %></p>
                          <p class="text-sm text-gray-500"><%= notification.message %></p>
                          <p class="text-xs text-gray-400 mt-1">
                            <time datetime="<%= notification.created_at.iso8601 %>">
                              <%= time_ago_in_words(notification.created_at) %> ago
                            </time>
                          </p>
                        </div>
                      </div>
                    </div>
                  <% end %>
                  <div class="p-4 text-center">
                    <%= link_to "View all notifications", notifications_path, 
                        class: "text-sm text-primary hover:text-blue-700" %>
                  </div>
                <% else %>
                  <div class="p-8 text-center">
                    <i class="fas fa-bell-slash text-gray-300 text-3xl mb-3" aria-hidden="true"></i>
                    <p class="text-gray-500">No notifications yet</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>

        <!-- User Menu -->
        <div class="relative" data-controller="dropdown">
          <button type="button" 
                  data-action="click->dropdown#toggle"
                  class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200"
                  aria-label="User menu for <%= current_user.display_name %>"
                  aria-expanded="false"
                  aria-haspopup="true">
            
            <!-- User Avatar -->
            <div class="relative">
              <% if current_user.avatar.present? %>
                <%= image_tag current_user.avatar, 
                    class: "h-8 w-8 rounded-full object-cover",
                    alt: "#{current_user.display_name} profile picture" %>
              <% else %>
                <div class="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center"
                     title="<%= current_user.display_name %>">
                  <span class="text-sm font-medium text-white" aria-hidden="true">
                    <%= current_user.initials %>
                  </span>
                </div>
              <% end %>
              
              <!-- Role Badge -->
              <div class="absolute -bottom-1 -right-1 h-4 w-4 <%= role_badge_color(current_user.role) %> rounded-full border-2 border-white flex items-center justify-center"
                   title="<%= current_user.role&.humanize || 'Member' %>">
                <i class="<%= role_icon(current_user.role) %> text-xs text-white" aria-hidden="true"></i>
              </div>
            </div>

            <!-- User Info (Desktop) -->
            <div class="hidden sm:block text-left">
              <div class="text-sm font-medium text-gray-900">
                <%= current_user.display_name %>
              </div>
              <div class="text-xs text-gray-500 capitalize">
                <%= current_user.role&.humanize || 'Member' %>
                <% if current_user.tenant %>
                  • <%= current_user.tenant.name.truncate(15) %>
                <% end %>
              </div>
            </div>

            <i class="fas fa-chevron-down text-gray-400 text-sm" aria-hidden="true"></i>
          </button>

          <!-- User Dropdown Menu -->
          <div data-dropdown-target="menu"
               class="hidden absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
               role="menu"
               aria-labelledby="user-menu">
            
            <!-- User Info Header -->
            <div class="p-4 border-b border-gray-200">
              <div class="flex items-center space-x-3">
                <% if current_user.avatar.present? %>
                  <%= image_tag current_user.avatar, 
                      class: "h-12 w-12 rounded-full object-cover",
                      alt: "#{current_user.display_name} profile picture" %>
                <% else %>
                  <div class="h-12 w-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
                    <span class="text-lg font-medium text-white">
                      <%= current_user.initials %>
                    </span>
                  </div>
                <% end %>
                
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    <%= current_user.display_name %>
                  </p>
                  <p class="text-sm text-gray-500 truncate">
                    <%= current_user.email %>
                  </p>
                  <div class="flex items-center mt-1">
                    <div class="h-2 w-2 <%= role_badge_color(current_user.role) %> rounded-full mr-2"></div>
                    <span class="text-xs text-gray-500 capitalize">
                      <%= current_user.role&.humanize || 'Member' %>
                      <% if current_user.tenant %>
                        • <%= current_user.tenant.name %>
                      <% end %>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Menu Items -->
            <div class="py-2">
              <%= dropdown_link "Profile Settings", edit_user_registration_path, 
                  "fas fa-user-cog", policy(:user).edit? %>
              
              <% if policy(:billing).show? %>
                <%= dropdown_link "Billing & Plans", billing_path, 
                    "fas fa-credit-card", true %>
              <% end %>
              
              <% if policy(:team).index? %>
                <%= dropdown_link "Team Members", team_path, 
                    "fas fa-users", true %>
              <% end %>
              
              <% if policy(:api_key).index? %>
                <%= dropdown_link "API Keys", api_keys_path, 
                    "fas fa-key", true %>
              <% end %>
              
              <% if current_user.admin? || current_user.owner? %>
                <%= dropdown_link "Tenant Settings", tenant_settings_path, 
                    "fas fa-building", true %>
              <% end %>
              
              <div class="border-t border-gray-100 my-2" role="separator"></div>
              
              <%= dropdown_link "Help Center", help_path, 
                  "fas fa-question-circle", true %>
              
              <%= dropdown_link "Keyboard Shortcuts", shortcuts_path, 
                  "fas fa-keyboard", true %>
              
              <%= dropdown_link "What's New", changelog_path, 
                  "fas fa-star", true %>
              
              <div class="border-t border-gray-100 my-2" role="separator"></div>
              
              <!-- Theme Toggle -->
              <div class="px-4 py-2" data-controller="theme-toggle">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-700 flex items-center">
                    <i class="fas fa-moon mr-3 text-gray-400" aria-hidden="true"></i>
                    Dark Mode
                  </span>
                  <button type="button" 
                          data-action="click->theme-toggle#toggle"
                          data-theme-toggle-target="switch"
                          class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                          role="switch"
                          aria-checked="false"
                          aria-label="Toggle dark mode">
                    <span class="sr-only">Toggle dark mode</span>
                    <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1"></span>
                  </button>
                </div>
              </div>
              
              <div class="border-t border-gray-100 my-2" role="separator"></div>
              
              <%= button_to destroy_user_session_path, 
                  method: :delete,
                  class: "w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 flex items-center",
                  "aria-label": "Sign out of your account" do %>
                <i class="fas fa-sign-out-alt mr-3" aria-hidden="true"></i>
                Sign Out
              <% end %>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <div class="md:hidden">
          <button type="button" 
                  data-action="click->navbar#toggleMobile"
                  data-navbar-target="mobileButton"
                  class="p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200"
                  aria-label="Toggle mobile menu"
                  aria-expanded="false">
            <span class="sr-only">Open main menu</span>
            <i data-navbar-target="hamburger" class="fas fa-bars text-lg" aria-hidden="true"></i>
            <i data-navbar-target="close" class="fas fa-times text-lg hidden" aria-hidden="true"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div data-navbar-target="mobileMenu" 
       class="hidden md:hidden border-t border-gray-200 bg-white"
       role="navigation"
       aria-label="Mobile navigation">
    <div class="px-4 pt-4 pb-6 space-y-3">
      
      <!-- Mobile Search -->
      <% if policy(:search).perform? %>
        <div class="relative">
          <label for="mobile-search" class="sr-only">Search campaigns, contacts, and content</label>
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="fas fa-search text-gray-400" aria-hidden="true"></i>
          </div>
          <input type="text" 
                 id="mobile-search"
                 placeholder="Search..."
                 class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
      <% end %>

      <!-- Mobile Navigation Links -->
      <div class="space-y-1" role="menu">
        <%= mobile_nav_link "Dashboard", dashboard_path, "fas fa-tachometer-alt", true %>
        
        <% if policy(:campaign).index? %>
          <%= mobile_nav_link "Campaigns", campaigns_path, "fas fa-bullhorn", true %>
        <% end %>
        
        <% if policy(:analytic).index? %>
          <%= mobile_nav_link "Analytics", analytics_path, "fas fa-chart-bar", true %>
        <% end %>
        
        <% if policy(:automation).index? %>
          <%= mobile_nav_link "Automation", automation_path, "fas fa-robot", true %>
        <% end %>
        
        <% if policy(:contact).index? %>
          <%= mobile_nav_link "Contacts", contacts_path, "fas fa-users", true %>
        <% end %>
        
        <% if policy(:content).index? %>
          <%= mobile_nav_link "Content", content_path, "fas fa-file-alt", true %>
        <% end %>
        
        <% if current_user.admin? || current_user.owner? %>
          <%= mobile_nav_link "Settings", settings_path, "fas fa-cog", true %>
        <% end %>
      </div>

      <div class="border-t border-gray-200 pt-4" role="separator">
        <%= mobile_nav_link "Profile Settings", edit_user_registration_path, "fas fa-user-cog", policy(:user).edit? %>
        <%= mobile_nav_link "Help Center", help_path, "fas fa-question-circle", true %>
        
        <%= button_to destroy_user_session_path, 
            method: :delete,
            class: "w-full text-left flex items-center px-3 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200" do %>
          <i class="fas fa-sign-out-alt mr-3" aria-hidden="true"></i>
          Sign Out
        <% end %>
      </div>
    </div>
  </div>
</nav>
<% end %>