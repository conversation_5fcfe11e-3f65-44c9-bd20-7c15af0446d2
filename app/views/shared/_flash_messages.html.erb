<div class="fixed top-20 right-4 z-40 space-y-2 max-w-sm w-full pointer-events-none">
  <% flash.each do |type, message| %>
    <% next if message.blank? %>
    <div data-controller="flash-message" 
         data-flash-message-duration-value="5000"
         class="<%= flash_class(type) %> border rounded-lg p-4 shadow-lg transform transition-all duration-300 translate-x-full opacity-0 pointer-events-auto"
         data-flash-message-target="container"
         role="alert"
         aria-live="assertive">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <i class="<%= flash_icon(type) %>" aria-hidden="true"></i>
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium"><%= message %></p>
        </div>
        <div class="ml-4 flex-shrink-0">
          <button data-action="click->flash-message#dismiss" 
                  class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded"
                  aria-label="Dismiss notification">
            <span class="sr-only">Close</span>
            <i class="fas fa-times text-sm" aria-hidden="true"></i>
          </button>
        </div>
      </div>
    </div>
  <% end %>
</div>
