<nav class="z-50 bg-white/90 backdrop-blur-md border-b border-gray-100 sticky top-0">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <%= link_to root_path, class: "flex items-center space-x-3" do %>
          <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center transform hover:scale-105 transition-transform duration-200">
            <i class="fas fa-robot text-white text-lg"></i>
          </div>
          <span class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            MarketingAI
          </span>
        <% end %>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <%= link_to root_path, 
            class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200 #{'text-indigo-600' if current_page?(root_path)}" do %>
          Home
        <% end %>
        
        <%= link_to about_path, 
            class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200 #{'text-indigo-600' if current_page?(about_path)}" do %>
          About
        <% end %>
        
        <a href="/#features" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">Features</a>
        <a href="/#pricing" class="text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200">Pricing</a>
        
        <%= link_to contact_us_path, 
            class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200 #{'text-indigo-600' if current_page?(contact_us_path)}" do %>
          Contact
        <% end %>
        
        <div class="flex items-center space-x-4 ml-8 border-l border-gray-200 pl-8">
          <% if user_signed_in? %>
            <%= link_to dashboard_path, 
                class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200" do %>
              Dashboard
            <% end %>
            <%= link_to destroy_user_session_path, method: :delete,
                class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200" do %>
              Sign Out
            <% end %>
          <% else %>
            <%= link_to new_user_session_path, 
                class: "text-gray-600 hover:text-indigo-600 font-medium transition-colors duration-200" do %>
              Sign In
            <% end %>
            <%= link_to new_user_registration_path, 
                class: "bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg hover:shadow-indigo-500/25 transform hover:scale-105 transition-all duration-200" do %>
              Start Free Trial
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button class="md:hidden p-2 rounded-lg text-gray-600 hover:text-indigo-600 hover:bg-gray-100 transition-colors duration-200"
              data-action="click->navigation#toggleMobileMenu"
              data-navigation-target="mobileMenuButton"
              aria-label="Toggle mobile menu">
        <i class="fas fa-bars text-xl"></i>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div class="md:hidden bg-white border-t border-gray-100 hidden" data-navigation-target="mobileMenu">
    <div class="px-4 py-6 space-y-4">
      <%= link_to root_path, 
          class: "block text-gray-600 hover:text-indigo-600 font-medium py-2 #{'text-indigo-600' if current_page?(root_path)}" do %>
        Home
      <% end %>
      
      <%= link_to about_path, 
          class: "block text-gray-600 hover:text-indigo-600 font-medium py-2 #{'text-indigo-600' if current_page?(about_path)}" do %>
        About
      <% end %>
      
      <a href="/#features" class="block text-gray-600 hover:text-indigo-600 font-medium py-2">Features</a>
      <a href="/#pricing" class="block text-gray-600 hover:text-indigo-600 font-medium py-2">Pricing</a>
      
      <%= link_to contact_us_path, 
          class: "block text-gray-600 hover:text-indigo-600 font-medium py-2 #{'text-indigo-600' if current_page?(contact_us_path)}" do %>
        Contact
      <% end %>
      
      <div class="pt-4 border-t border-gray-200 space-y-3">
        <% if user_signed_in? %>
          <%= link_to dashboard_path, 
              class: "block text-center text-gray-600 hover:text-indigo-600 font-medium py-2" do %>
            Dashboard
          <% end %>
          <%= link_to destroy_user_session_path, method: :delete,
              class: "block text-center text-gray-600 hover:text-indigo-600 font-medium py-2" do %>
            Sign Out
          <% end %>
        <% else %>
          <%= link_to new_user_session_path, 
              class: "block text-center text-gray-600 hover:text-indigo-600 font-medium py-2" do %>
            Sign In
          <% end %>
          <%= link_to new_user_registration_path, 
              class: "block bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold text-center" do %>
            Start Free Trial
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</nav>