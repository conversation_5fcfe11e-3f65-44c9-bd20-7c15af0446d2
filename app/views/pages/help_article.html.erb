<% content_for :title, "Help Article - MarketingAI" %>
<% content_for :description, "Detailed help article with step-by-step instructions and solutions." %>

<div class="min-h-screen bg-gray-50">
  <div class="container mx-auto px-6 py-12">
    <div class="max-w-4xl mx-auto">
      <!-- Breadcrumb -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li><a href="<%= help_path %>" class="hover:text-blue-600">Help Center</a></li>
          <li><span class="mx-2">/</span></li>
          <li><a href="<%= help_category_path(category: params[:category]) %>" class="hover:text-blue-600"><%= params[:category]&.humanize || "Category" %></a></li>
          <li><span class="mx-2">/</span></li>
          <li class="text-gray-900 font-medium"><%= params[:slug]&.humanize || "Article" %></li>
        </ol>
      </nav>

      <!-- Article Header -->
      <div class="mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          <%= params[:slug]&.humanize || "Help Article" %>
        </h1>
        <div class="flex items-center text-sm text-gray-500 space-x-4">
          <span>Last updated: <%= Date.current.strftime("%B %d, %Y") %></span>
          <span>•</span>
          <span>5 min read</span>
        </div>
      </div>

      <!-- Article Content -->
      <div class="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
        <div class="prose prose-lg max-w-none">
          <h2>Overview</h2>
          <p>
            This article provides comprehensive information about the selected topic. 
            Follow the step-by-step instructions below to resolve your issue or learn about the feature.
          </p>

          <h2>Getting Started</h2>
          <p>
            Before proceeding, make sure you have the necessary permissions and access to the required features.
          </p>

          <ol>
            <li>Navigate to your dashboard</li>
            <li>Select the appropriate section</li>
            <li>Follow the on-screen instructions</li>
            <li>Save your changes</li>
          </ol>

          <h2>Troubleshooting</h2>
          <p>
            If you encounter any issues, try the following solutions:
          </p>

          <ul>
            <li>Clear your browser cache and cookies</li>
            <li>Ensure you're using a supported browser</li>
            <li>Check your internet connection</li>
            <li>Contact support if the issue persists</li>
          </ul>

          <h2>Additional Resources</h2>
          <p>
            For more information, you can also check out our other help articles or contact our support team.
          </p>
        </div>
      </div>

      <!-- Helpful Actions -->
      <div class="mt-8 bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Was this article helpful?</h3>
        <div class="flex items-center space-x-4">
          <button class="px-4 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors">
            👍 Yes, helpful
          </button>
          <button class="px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors">
            👎 Not helpful
          </button>
        </div>
      </div>

      <!-- Navigation -->
      <div class="mt-12 flex justify-between">
        <a href="<%= help_category_path(category: params[:category]) %>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
          ← Back to <%= params[:category]&.humanize || "Category" %>
        </a>
        <a href="<%= contact_us_path %>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
          Contact Support →
        </a>
      </div>
    </div>
  </div>
</div>
