<div class="min-h-screen bg-white" data-controller="contact-page">
  
  <!-- Navigation -->
  <%= render 'shared/navbar' %>

  <!-- Hero Section with Multi-Option Contact -->
  <section class="relative bg-gradient-to-br from-indigo-50 via-white to-purple-50 pt-20 pb-16 overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-32 w-96 h-96 bg-gradient-to-br from-purple-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
      <div class="absolute -bottom-40 -left-32 w-96 h-96 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center" data-controller="fade-in">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-8">
          <i class="fas fa-headset mr-2"></i>
          Get Expert Support
        </div>

        <!-- Main Headline -->
        <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
          We're Here to 
          <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Help You Succeed
          </span>
        </h1>

        <!-- Subheadline -->
        <p class="text-xl text-gray-600 mb-12 leading-relaxed max-w-4xl mx-auto">
          Choose how you'd like to connect with our team of marketing AI experts. From quick questions to detailed consultations, we're ready to help you transform your marketing.
        </p>

        <!-- Multi-Option Contact Hero -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          <!-- Live Chat -->
          <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
               data-action="click->contact-page#openLiveChat">
            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
              <i class="fas fa-comments text-white text-2xl"></i>
            </div>
            <h3 class="font-bold text-gray-900 mb-2 text-lg">Live Chat</h3>
            <p class="text-gray-600 text-sm mb-4">Get instant answers from our support team</p>
            <div class="flex items-center justify-center">
              <span class="text-green-600 font-semibold text-sm">Available Now</span>
              <div class="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse"></div>
            </div>
            <p class="text-xs text-gray-500 mt-2">Response time: < 2 minutes</p>
          </div>

          <!-- Email Support -->
          <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
              <i class="fas fa-envelope text-white text-2xl"></i>
            </div>
            <h3 class="font-bold text-gray-900 mb-2 text-lg">Email Support</h3>
            <p class="text-gray-600 text-sm mb-4">Detailed help for complex questions</p>
            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-semibold text-sm">
              <EMAIL>
            </a>
            <p class="text-xs text-gray-500 mt-2">Response time: < 4 hours</p>
          </div>

          <!-- Phone Support -->
          <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
              <i class="fas fa-phone text-white text-2xl"></i>
            </div>
            <h3 class="font-bold text-gray-900 mb-2 text-lg">Phone Support</h3>
            <p class="text-gray-600 text-sm mb-4">Direct line for urgent matters</p>
            <a href="tel:******-555-0123" class="text-purple-600 hover:text-purple-700 font-semibold text-sm">
              +****************
            </a>
            <p class="text-xs text-gray-500 mt-2">Mon-Fri: 9AM-6PM EST</p>
          </div>

          <!-- Schedule Demo -->
          <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
               data-action="click->contact-page#scheduleDemo">
            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
              <i class="fas fa-calendar text-white text-2xl"></i>
            </div>
            <h3 class="font-bold text-gray-900 mb-2 text-lg">Book Demo</h3>
            <p class="text-gray-600 text-sm mb-4">Personalized platform walkthrough</p>
            <span class="text-orange-600 font-semibold text-sm">Schedule Now →</span>
            <p class="text-xs text-gray-500 mt-2">30-min sessions available</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Form & Office Locations Section -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
        
        <!-- Advanced Contact Form -->
        <div data-controller="fade-in" data-fade-in-delay-value="100">
          <div class="bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 shadow-xl">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-paper-plane text-white text-xl"></i>
              </div>
              <div>
                <h2 class="text-3xl font-bold text-gray-900">Send us a message</h2>
                <p class="text-gray-600">Get a response within 4 hours</p>
              </div>
            </div>

            <%= form_with url: contact_messages_path, method: :post, local: false,
                data: { 
                  controller: "contact-form", 
                  action: "submit->contact-form#submit",
                  turbo_action: "replace"
                },
                class: "space-y-6" do |form| %>
              
              <!-- Name & Email Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <%= form.label :name, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-user mr-2 text-indigo-500"></i>Full Name <span class="text-red-500">*</span>
                  <% end %>
                  <%= form.text_field :name, 
                      required: true,
                      class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                      placeholder: "John Doe",
                      data: { contact_form_target: "name" } %>
                  <div class="mt-1 text-sm text-red-600 hidden" data-contact-form-target="nameError"></div>
                </div>

                <div>
                  <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-envelope mr-2 text-indigo-500"></i>Email Address <span class="text-red-500">*</span>
                  <% end %>
                  <%= form.email_field :email, 
                      required: true,
                      class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                      placeholder: "<EMAIL>",
                      data: { contact_form_target: "email" } %>
                  <div class="mt-1 text-sm text-red-600 hidden" data-contact-form-target="emailError"></div>
                </div>
              </div>

              <!-- Company & Phone Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <%= form.label :company, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-building mr-2 text-indigo-500"></i>Company Name
                  <% end %>
                  <%= form.text_field :company, 
                      class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                      placeholder: "Your Company Inc.",
                      data: { contact_form_target: "company" } %>
                </div>

                <div>
                  <%= form.label :phone, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-phone mr-2 text-indigo-500"></i>Phone Number
                  <% end %>
                  <%= form.telephone_field :phone, 
                      class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                      placeholder: "+****************",
                      data: { contact_form_target: "phone" } %>
                </div>
              </div>

              <!-- Subject & Team Size Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <%= form.label :subject, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-tag mr-2 text-indigo-500"></i>How can we help? <span class="text-red-500">*</span>
                  <% end %>
                  <%= form.select :subject, 
                      options_for_select([
                        ['General Question', 'general'],
                        ['Sales & Pricing', 'sales'],
                        ['Technical Support', 'support'],
                        ['Schedule Demo', 'demo'],
                        ['Partnership Inquiry', 'partnership'],
                        ['Press & Media', 'press'],
                        ['Feature Request', 'feature'],
                        ['Bug Report', 'bug'],
                        ['Other', 'other']
                      ], 'general'),
                      {},
                      { 
                        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                        data: { contact_form_target: "subject" }
                      } %>
                </div>

                <div>
                  <%= form.label :team_size, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                    <i class="fas fa-users mr-2 text-indigo-500"></i>Team Size
                  <% end %>
                  <%= form.select :team_size, 
                      options_for_select([
                        ['Just me', 'solo'],
                        ['2-10 people', 'small'],
                        ['11-50 people', 'medium'],
                        ['51-200 people', 'large'],
                        ['200+ people', 'enterprise']
                      ]),
                      { include_blank: 'Select team size' },
                      { 
                        class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400",
                        data: { contact_form_target: "teamSize" }
                      } %>
                </div>
              </div>

              <!-- Message Field -->
              <div>
                <%= form.label :message, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
                  <i class="fas fa-comment mr-2 text-indigo-500"></i>Tell us more about your needs <span class="text-red-500">*</span>
                <% end %>
                <%= form.text_area :message, 
                    required: true,
                    rows: 5,
                    class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:border-gray-400 resize-none",
                    placeholder: "Describe your marketing challenges, goals, or specific questions about MarketingAI...",
                    data: { contact_form_target: "message" } %>
                <div class="mt-1 text-sm text-red-600 hidden" data-contact-form-target="messageError"></div>
                <div class="mt-1 text-xs text-gray-500">Minimum 20 characters</div>
              </div>

              <!-- Checkboxes -->
              <div class="space-y-3">
                <div class="flex items-start">
                  <%= form.check_box :marketing_consent, 
                      class: "mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                      data: { contact_form_target: "marketing" } %>
                  <%= form.label :marketing_consent, class: "ml-3 text-sm text-gray-600" do %>
                    I'd like to receive marketing updates and product announcements from MarketingAI
                  <% end %>
                </div>

                <div class="flex items-start">
                  <%= form.check_box :privacy_consent, 
                      required: true,
                      class: "mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",
                      data: { contact_form_target: "privacy" } %>
                  <%= form.label :privacy_consent, class: "ml-3 text-sm text-gray-600" do %>
                    I agree to the <a href="/privacy" class="text-indigo-600 hover:text-indigo-500 underline" target="_blank">Privacy Policy</a> and 
                    <a href="/terms" class="text-indigo-600 hover:text-indigo-500 underline" target="_blank">Terms of Service</a> <span class="text-red-500">*</span>
                  <% end %>
                  <div class="mt-1 text-sm text-red-600 hidden" data-contact-form-target="privacyError"></div>
                </div>
              </div>

              <!-- Submit Button -->
              <div>
                <%= form.submit "Send Message", 
                    class: "w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-6 rounded-lg font-bold text-lg hover:shadow-lg hover:shadow-indigo-500/25 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
                    data: { contact_form_target: "submit" } %>
              </div>

              <!-- Success/Error Messages -->
              <div class="hidden p-4 rounded-lg bg-green-50 border border-green-200" data-contact-form-target="successMessage">
                <div class="flex items-center">
                  <i class="fas fa-check-circle text-green-500 mr-3 text-xl"></i>
                  <div>
                    <span class="text-green-800 font-semibold">Message sent successfully!</span>
                    <p class="text-green-700 text-sm mt-1">We'll get back to you within 4 hours during business hours.</p>
                  </div>
                </div>
              </div>

              <div class="hidden p-4 rounded-lg bg-red-50 border border-red-200" data-contact-form-target="errorMessage">
                <div class="flex items-center">
                  <i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>
                  <span class="text-red-800 font-semibold">There was an error sending your message. Please try again.</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Office Locations with Google Maps -->
        <div data-controller="fade-in" data-fade-in-delay-value="300">
          <h2 class="text-3xl font-bold text-gray-900 mb-8">Visit Our Offices</h2>
          
          <!-- Office Location Cards -->
          <div class="space-y-6 mb-8">
            <!-- San Francisco HQ -->
            <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-start">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                  <i class="fas fa-map-marker-alt text-white text-xl"></i>
                </div>
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-bold text-gray-900 text-lg">San Francisco (HQ)</h4>
                    <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-700 text-xs font-medium rounded-full">Headquarters</span>
                  </div>
                  <p class="text-gray-600 mb-3">
                    <i class="fas fa-building mr-2"></i>123 Innovation Drive, Suite 400<br>
                    <i class="fas fa-location-arrow mr-2"></i>San Francisco, CA 94105<br>
                    <i class="fas fa-flag mr-2"></i>United States
                  </p>
                  <div class="flex items-center space-x-4 text-sm">
                    <a href="https://maps.google.com/?q=123+Innovation+Drive+San+Francisco+CA" target="_blank" 
                       class="text-indigo-600 hover:text-indigo-700 font-medium flex items-center">
                      <i class="fas fa-directions mr-1"></i>Get Directions
                    </a>
                    <span class="text-gray-500">•</span>
                    <span class="text-gray-600"><i class="fas fa-clock mr-1"></i>Mon-Fri: 9AM-6PM PST</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- New York Office -->
            <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-start">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                  <i class="fas fa-map-marker-alt text-white text-xl"></i>
                </div>
                <div class="flex-1">
                  <h4 class="font-bold text-gray-900 text-lg mb-2">New York</h4>
                  <p class="text-gray-600 mb-3">
                    <i class="fas fa-building mr-2"></i>456 Business Avenue, Floor 12<br>
                    <i class="fas fa-location-arrow mr-2"></i>New York, NY 10001<br>
                    <i class="fas fa-flag mr-2"></i>United States
                  </p>
                  <div class="flex items-center space-x-4 text-sm">
                    <a href="https://maps.google.com/?q=456+Business+Avenue+New+York+NY" target="_blank" 
                       class="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                      <i class="fas fa-directions mr-1"></i>Get Directions
                    </a>
                    <span class="text-gray-500">•</span>
                    <span class="text-gray-600"><i class="fas fa-clock mr-1"></i>Mon-Fri: 9AM-6PM EST</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- London Office -->
            <div class="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-start">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                  <i class="fas fa-map-marker-alt text-white text-xl"></i>
                </div>
                <div class="flex-1">
                  <h4 class="font-bold text-gray-900 text-lg mb-2">London</h4>
                  <p class="text-gray-600 mb-3">
                    <i class="fas fa-building mr-2"></i>789 Tech Street, 3rd Floor<br>
                    <i class="fas fa-location-arrow mr-2"></i>London, SW1A 1AA<br>
                    <i class="fas fa-flag mr-2"></i>United Kingdom
                  </p>
                  <div class="flex items-center space-x-4 text-sm">
                    <a href="https://maps.google.com/?q=789+Tech+Street+London+SW1A+1AA" target="_blank" 
                       class="text-green-600 hover:text-green-700 font-medium flex items-center">
                      <i class="fas fa-directions mr-1"></i>Get Directions
                    </a>
                    <span class="text-gray-500">•</span>
                    <span class="text-gray-600"><i class="fas fa-clock mr-1"></i>Mon-Fri: 9AM-5PM GMT</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Interactive Google Map -->
          <div class="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
            <div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4 flex items-center justify-between">
              <div class="flex items-center">
                <i class="fas fa-map text-xl mr-3"></i>
                <span class="font-semibold">Our Global Offices</span>
              </div>
              <button class="text-white hover:text-gray-200 text-sm" 
                      data-action="click->contact-page#toggleMapView"
                      data-contact-page-target="mapToggle">
                <i class="fas fa-expand-arrows-alt mr-1"></i>
                Expand
              </button>
            </div>
            
            <!-- Google Maps Embed -->
            <div class="h-80" data-contact-page-target="mapContainer">
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d387191.33750346623!2d-122.69181249013298!3d37.77492954328302!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80859a6d00690021%3A0x4a501367f076adff!2sSan%20Francisco%2C%20CA!5e0!3m2!1sen!2sus!4v1640995200000!5m2!1sen!2sus"
                width="100%" 
                height="100%" 
                style="border:0;" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade"
                class="w-full h-full">
              </iframe>
            </div>
          </div>

          <!-- Contact Preferences -->
          <div class="mt-8 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-200">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
              <i class="fas fa-clock mr-2 text-indigo-600"></i>
              When to Expect Our Response
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong class="text-indigo-700">Sales Inquiries:</strong>
                <p class="text-gray-600">Within 2 hours during business hours</p>
              </div>
              <div>
                <strong class="text-indigo-700">Technical Support:</strong>
                <p class="text-gray-600">Within 4 hours (24/7 for critical issues)</p>
              </div>
              <div>
                <strong class="text-indigo-700">General Questions:</strong>
                <p class="text-gray-600">Within 24 hours</p>
              </div>
              <div>
                <strong class="text-indigo-700">Partnership Inquiries:</strong>
                <p class="text-gray-600">Within 1 business day</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section with Smooth Animations -->
  <section class="py-24 bg-gradient-to-b from-gray-50 to-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16" data-controller="fade-in">
        <div class="inline-flex items-center px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium mb-6">
          <i class="fas fa-question-circle mr-2"></i>
          Frequently Asked Questions
        </div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">Quick Answers to Common Questions</h2>
        <p class="text-xl text-gray-600">
          Find answers to the most frequently asked questions about MarketingAI. Still need help? Contact us directly.
        </p>
      </div>

      <div class="space-y-4" data-controller="faq-accordion">
        <!-- FAQ Items -->
        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="100">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">How quickly can I get started with MarketingAI?</span>
            <div class="flex items-center">
              <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
            </div>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed">
                You can get started in just 5 minutes! Simply sign up for a free trial, connect your existing tools (like your email provider or CRM), and our AI will immediately start analyzing your data. Most customers see their first automated campaign launch within 24 hours of signing up. Our onboarding team can also provide a guided setup session if needed.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="200">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">Do I need technical expertise to use MarketingAI?</span>
            <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed">
                Not at all! MarketingAI is designed for marketers, not developers. Our intuitive interface guides you through every step, and our AI handles all the complex technical work behind the scenes. If you can use email or social media, you can use MarketingAI. Plus, our support team and extensive documentation are always available to help you succeed.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="300">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">What integrations and tools do you support?</span>
            <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed mb-4">
                We integrate with 100+ popular tools including:
              </p>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Salesforce</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>HubSpot</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Shopify</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mailchimp</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Google Analytics</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Facebook Ads</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>LinkedIn</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Slack</div>
                <div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Zapier</div>
              </div>
              <p class="text-gray-600 mt-4">
                Don't see an integration you need? Our team can often build custom connections for Enterprise customers.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="400">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">How secure is my data with MarketingAI?</span>
            <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed mb-4">
                Security is our top priority. We maintain enterprise-grade security with:
              </p>
              <ul class="space-y-2 text-gray-600">
                <li class="flex items-center"><i class="fas fa-shield-alt text-green-500 mr-3"></i>SOC 2 Type II compliance</li>
                <li class="flex items-center"><i class="fas fa-lock text-green-500 mr-3"></i>End-to-end encryption</li>
                <li class="flex items-center"><i class="fas fa-user-shield text-green-500 mr-3"></i>GDPR and CCPA compliance</li>
                <li class="flex items-center"><i class="fas fa-certificate text-green-500 mr-3"></i>ISO 27001 certification</li>
                <li class="flex items-center"><i class="fas fa-server text-green-500 mr-3"></i>Secure, redundant data centers</li>
              </ul>
              <p class="text-gray-600 mt-4">
                We never share your data with third parties and you maintain full control over your information.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="500">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">What kind of results can I expect?</span>
            <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed mb-4">
                Our customers typically see significant improvements within the first month:
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div class="text-2xl font-bold text-green-600">127%</div>
                  <div class="text-sm text-green-700">Average ROI increase</div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div class="text-2xl font-bold text-blue-600">89%</div>
                  <div class="text-sm text-blue-700">Higher email open rates</div>
                </div>
                <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                  <div class="text-2xl font-bold text-purple-600">65%</div>
                  <div class="text-sm text-purple-700">More qualified leads</div>
                </div>
                <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                  <div class="text-2xl font-bold text-orange-600">40%</div>
                  <div class="text-sm text-orange-700">Time saved on manual tasks</div>
                </div>
              </div>
              <p class="text-gray-600 mt-4">
                Results vary by industry and implementation, but we're confident you'll see measurable improvements quickly.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden" data-controller="fade-in" data-fade-in-delay-value="600">
          <button class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  data-action="click->faq-accordion#toggle"
                  data-faq-accordion-target="trigger">
            <span class="font-semibold text-gray-900 text-lg">Can I cancel my subscription anytime?</span>
            <i class="fas fa-chevron-down text-gray-400 transition-transform duration-300" data-faq-accordion-target="icon"></i>
          </button>
          <div class="hidden" data-faq-accordion-target="content">
            <div class="px-8 pb-6">
              <p class="text-gray-600 leading-relaxed">
                Yes, you have complete flexibility. You can cancel your subscription at any time with no cancellation fees or penalties. Your account will remain active until the end of your current billing period, and you can export all your data. We also offer a 30-day money-back guarantee if you're not completely satisfied with MarketingAI.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Still Have Questions CTA -->
      <div class="text-center mt-12" data-controller="fade-in" data-fade-in-delay-value="700">
        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8 border border-indigo-200">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Still have questions?</h3>
          <p class="text-gray-600 mb-6">Our team is here to help you succeed with MarketingAI</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200"
                    data-action="click->contact-page#openLiveChat">
              <i class="fas fa-comments mr-2"></i>
              Start Live Chat
            </button>
            <a href="mailto:<EMAIL>" 
               class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
              <i class="fas fa-envelope mr-2"></i>
              Email Support
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Support Resources Grid -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16" data-controller="fade-in">
        <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6">
          <i class="fas fa-graduation-cap mr-2"></i>
          Self-Service Resources
        </div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">Get Help When You Need It</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive resources to help you master MarketingAI and get the most out of your marketing automation.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Help Center -->
        <div class="group bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="100">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-book-open text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">📚 Help Center</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Comprehensive guides, tutorials, and documentation covering every feature of MarketingAI.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">
              Browse Articles <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <span class="text-xs text-gray-500">250+ articles</span>
          </div>
        </div>

        <!-- Video Academy -->
        <div class="group bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="200">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-play-circle text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">🎥 Video Academy</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Step-by-step video tutorials from basic setup to advanced AI marketing strategies.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold">
              Watch Videos <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <span class="text-xs text-gray-500">50+ videos</span>
          </div>
        </div>

        <!-- Community Forum -->
        <div class="group bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="300">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-users text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">👥 Community</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Connect with other MarketingAI users, share best practices, and get peer support.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold">
              Join Discussion <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <span class="text-xs text-gray-500">15K+ members</span>
          </div>
        </div>

        <!-- API Documentation -->
        <div class="group bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8 border border-orange-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="400">
          <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-code text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">⚡ API Docs</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Complete API reference and developer guides for building custom integrations.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-orange-600 hover:text-orange-700 font-semibold">
              View API Docs <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <span class="text-xs text-gray-500">REST & GraphQL</span>
          </div>
        </div>

        <!-- Live Training -->
        <div class="group bg-gradient-to-br from-teal-50 to-cyan-50 rounded-2xl p-8 border border-teal-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="500">
          <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">📅 Live Training</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Weekly live training sessions and Q&A with MarketingAI experts and successful customers.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-teal-600 hover:text-teal-700 font-semibold">
              Register Now <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <span class="text-xs text-gray-500">Every Tuesday</span>
          </div>
        </div>

        <!-- System Status -->
        <div class="group bg-gradient-to-br from-gray-50 to-slate-50 rounded-2xl p-8 border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-controller="fade-in" data-fade-in-delay-value="600">
          <div class="w-16 h-16 bg-gradient-to-br from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-heartbeat text-white text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">💚 System Status</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Real-time status updates and incident reports for all MarketingAI services.
          </p>
          <div class="flex items-center justify-between">
            <a href="#" class="inline-flex items-center text-gray-600 hover:text-gray-700 font-semibold">
              Check Status <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
              <span class="text-xs text-green-600 font-medium">Operational</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Response Time Guarantees -->
  <section class="py-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 relative overflow-hidden">
    <div class="absolute inset-0">
      <div class="absolute top-0 left-1/4 w-64 h-64 bg-white rounded-full mix-blend-overlay filter blur-xl opacity-20 animate-blob"></div>
      <div class="absolute bottom-0 right-1/4 w-64 h-64 bg-white rounded-full mix-blend-overlay filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
    </div>

    <div class="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center" data-controller="fade-in">
      <div class="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-6">
        <i class="fas fa-stopwatch mr-2"></i>
        Our Response Time Guarantee
      </div>
      <h2 class="text-4xl font-bold text-white mb-4">We're Committed to Fast, Expert Support</h2>
      <p class="text-indigo-100 text-xl mb-12 max-w-3xl mx-auto">
        When you need help, we're here. Our response time guarantees ensure you get the support you need, when you need it.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-bolt text-white text-2xl"></i>
          </div>
          <div class="text-4xl font-bold text-white mb-2">< 2 hours</div>
          <div class="text-indigo-100 font-medium mb-2">Sales Inquiries</div>
          <div class="text-indigo-200 text-sm">Get pricing and demos fast</div>
        </div>
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-tools text-white text-2xl"></i>
          </div>
          <div class="text-4xl font-bold text-white mb-2">< 4 hours</div>
          <div class="text-indigo-100 font-medium mb-2">Technical Support</div>
          <div class="text-indigo-200 text-sm">Expert help when you're stuck</div>
        </div>
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
          </div>
          <div class="text-4xl font-bold text-white mb-2">< 30 min</div>
          <div class="text-indigo-100 font-medium mb-2">Critical Issues</div>
          <div class="text-indigo-200 text-sm">Emergency support 24/7</div>
        </div>
      </div>

      <div class="mt-12 bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
        <p class="text-white font-semibold mb-2">💯 100% Guarantee</p>
        <p class="text-indigo-100 text-sm">
          If we don't meet our response time guarantee, your next month is free. That's how confident we are in our support.
        </p>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <%= render 'shared/footer' %>

  <!-- Live Chat Widget -->
  <div class="fixed bottom-6 right-6 z-50 hidden" data-contact-page-target="chatWidget">
    <div class="bg-white rounded-2xl shadow-2xl border border-gray-200 w-80 h-96 flex flex-col overflow-hidden">
      <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-headset text-sm"></i>
          </div>
          <div>
            <span class="font-semibold">MarketingAI Support</span>
            <div class="text-xs opacity-90">We're online now</div>
          </div>
        </div>
        <button class="text-white hover:text-gray-200 p-1" data-action="click->contact-page#closeLiveChat">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="flex-1 p-4 bg-gray-50 overflow-y-auto">
        <div class="space-y-3">
          <div class="flex items-start">
            <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <i class="fas fa-robot text-white text-xs"></i>
            </div>
            <div class="bg-white rounded-lg p-3 shadow-sm max-w-xs">
              <p class="text-sm text-gray-800">Hi! 👋 How can I help you today?</p>
            </div>
          </div>
          
          <div class="flex justify-center">
            <div class="bg-blue-100 rounded-full px-3 py-1">
              <p class="text-xs text-blue-600">Typically replies in a few minutes</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="p-4 border-t border-gray-200 bg-white">
        <div class="flex space-x-2">
          <input type="text" 
                 placeholder="Type your message..." 
                 class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
          <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors duration-200">
            <i class="fas fa-paper-plane text-sm"></i>
          </button>
        </div>
        <p class="text-xs text-gray-500 mt-2 text-center">
          In production, this would connect to your actual chat system
        </p>
      </div>
    </div>
  </div>

  <!-- Floating Contact Buttons -->
  <div class="fixed bottom-6 left-6 z-50 space-y-3">
    <!-- Email Quick Access -->
    <a href="mailto:<EMAIL>" 
       class="flex items-center justify-center w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-200 transform hover:scale-110 group">
      <i class="fas fa-envelope text-lg"></i>
      <div class="absolute right-16 bg-gray-900 text-white px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
        Email Support
      </div>
    </a>
    
    <!-- Phone Quick Access -->
    <a href="tel:******-555-0123" 
       class="flex items-center justify-center w-14 h-14 bg-green-600 text-white rounded-full shadow-lg hover:bg-green-700 hover:shadow-xl transition-all duration-200 transform hover:scale-110 group">
      <i class="fas fa-phone text-lg"></i>
      <div class="absolute right-16 bg-gray-900 text-white px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
        Call Us
      </div>
    </a>
  </div>
</div>