<!-- app/views/layouts/application.html.erb -->
<!DOCTYPE html>
<html lang="<%= I18n.locale %>" class="h-full">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    
    <title><%= @page_title || page_title(yield(:title)) %></title>
    <meta name="description" content="<%= @page_description || yield(:description) || 'AI-powered marketing automation platform for growing businesses' %>">
    <% if @page_keywords %>
    <meta name="keywords" content="<%= @page_keywords %>">
    <% end %>
    <% if @canonical_url %>
    <link rel="canonical" href="<%= @canonical_url %>">
    <% end %>
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <!-- Application assets -->
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" 
          integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" 
          crossorigin="anonymous" 
          referrerpolicy="no-referrer">
    
    <!-- Favicon -->
    <%#= favicon_link_tag "favicon.ico" %>
    <%#= favicon_link_tag "favicon-32x32.png", sizes: "32x32" %>
    <%#= favicon_link_tag "favicon-16x16.png", sizes: "16x16" %>
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="<%= @og_title || page_title(yield(:title)) %>">
    <meta property="og:description" content="<%= @og_description || yield(:description) || 'AI-powered marketing automation platform for growing businesses' %>">
    <meta property="og:type" content="<%= @og_type || 'website' %>">
    <meta property="og:url" content="<%= @og_url || request.original_url %>">
    <% if @og_image %>
    <meta property="og:image" content="<%= @og_image %>">
    <% end %>
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="<%= @twitter_card || 'summary_large_image' %>">
    <% if @twitter_site %>
    <meta name="twitter:site" content="<%= @twitter_site %>">
    <% end %>
    <meta name="twitter:title" content="<%= @twitter_title || page_title(yield(:title)) %>">
    <meta name="twitter:description" content="<%= @twitter_description || yield(:description) || 'AI-powered marketing automation platform for growing businesses' %>">
    <% if @twitter_image %>
    <meta name="twitter:image" content="<%= @twitter_image %>">
    <% end %>
    
    <!-- Theme detection -->
    <script>
      // Prevent flash of wrong theme
      (function() {
        const theme = localStorage.getItem('theme') || 
                     (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    
    <!-- Additional head content -->
    <%= yield :head %>
  </head>

  <body class="h-full bg-gray-50 font-sans antialiased <%= theme_class %>" 
        data-controller="application"
        data-theme="<%= current_theme %>">
    
    <!-- Skip navigation for screen readers -->
    <%= skip_link("#main-content", "Skip to main content") %>
    <%= skip_link("#navbar", "Skip to navigation") if user_signed_in? %>
    
    <!-- Page loading indicator -->
    <div id="page-loading" 
         class="fixed top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-secondary z-50 hidden"
         data-turbo-permanent>
      <div class="h-full bg-white/30 animate-pulse"></div>
    </div>

    <div class="min-h-full flex flex-col">
      <% if user_signed_in? %>
        <!-- Main Application Layout -->
        
        <!-- Navigation -->
        <div id="navbar">
          <%= render 'shared/navbar' %>
        </div>
        
        <!-- Flash Messages -->
        <% if notice || alert %>
          <%= render 'shared/flash_messages' %>
        <% end %>
        
        <!-- Breadcrumbs (if provided) -->
        <% if yield(:breadcrumbs).present? %>
          <nav class="bg-white border-b border-gray-200" aria-label="Breadcrumb">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <ol class="flex items-center space-x-4 py-4" role="list">
                <%= yield :breadcrumbs %>
              </ol>
            </div>
          </nav>
        <% end %>
        
        <!-- Page Header (if provided) -->
        <% if yield(:page_header).present? %>
          <header class="bg-white border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
              <%= yield :page_header %>
            </div>
          </header>
        <% end %>
        
        <!-- Main Content -->
        <main id="main-content" 
              class="flex-1"
              role="main"
              aria-label="Main content">
          <%= yield %>
        </main>
        
        <!-- Footer -->
        <%= render 'shared/footer' %>
        
      <% else %>
        <!-- Guest Layout (Authentication pages) -->
        
        <!-- Simple Header for Guest Pages -->
        <header class="bg-white border-b border-gray-200" role="banner">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center h-16 items-center">
              <%= link_to root_path, class: "flex items-center space-x-3" do %>
                <div class="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center">
                  <i class="fas fa-chart-line text-white text-lg" aria-hidden="true"></i>
                </div>
                <span class="text-xl font-bold text-gray-900">MarketingAI</span>
              <% end %>
            </div>
          </div>
        </header>
        
        <!-- Flash Messages -->
        <% if notice || alert %>
          <%= render 'shared/flash_messages' %>
        <% end %>
        
        <!-- Guest Main Content -->
        <main id="main-content" 
              class="flex-1"
              role="main"
              aria-label="Main content">
          <%= yield %>
        </main>
        
        <!-- Simple Footer for Guest Pages -->
        <%= render 'shared/guest_footer' %>
      <% end %>
    </div>

    <!-- Global Modals Container -->
    <div id="modal-container" 
         class="fixed inset-0 z-50 hidden"
         data-controller="modal"
         role="dialog"
         aria-modal="true"
         aria-hidden="true">
    </div>

    <!-- Toast Notifications Container -->
    <div id="toast-container" 
         class="fixed bottom-4 right-4 z-40 space-y-2"
         aria-live="polite"
         aria-atomic="false">
    </div>

    <!-- Development helpers (only in development) -->
    <% if Rails.env.development? %>
      <div class="fixed bottom-4 left-4 z-30">
        <details class="bg-gray-800 text-white text-xs rounded p-2">
          <summary class="cursor-pointer">Debug Info</summary>
          <div class="mt-2 space-y-1">
            <div>User: <%= current_user&.email || 'Guest' %></div>
            <div>Tenant: <%= current_user&.tenant&.name || 'None' %></div>
            <div>Role: <%= current_user&.role || 'None' %></div>
            <div>Controller: <%= controller_name %></div>
            <div>Action: <%= action_name %></div>
          </div>
        </details>
      </div>
    <% end %>

    <!-- Service worker registration (for PWA features) -->
    <% if Rails.env.production? %>
      <script>
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.register('/service-worker.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        }
      </script>
    <% end %>
  </body>
</html>
