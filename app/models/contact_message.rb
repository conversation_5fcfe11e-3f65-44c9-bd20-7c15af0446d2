class ContactMessage < ApplicationRecord
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :email, presence: true,
                   format: { with: URI::MailTo::EMAIL_REGEXP },
                   length: { maximum: 255 }
  validates :subject, presence: true, inclusion: {
    in: %w[general sales support partnership press feature bug other]
  }
  validates :message, presence: true, length: { minimum: 10, maximum: 5000 }
    validates :privacy_consent, acceptance: true

    validates :company, length: { maximum: 100 }, allow_blank: true
    validates :ip_address, presence: true
    validates :user_agent, presence: true

    before_validation :normalize_email
    before_validation :set_defaults

    scope :recent, -> { order(created_at: :desc) }
    scope :by_subject, ->(subject) { where(subject: subject) }
    scope :unread, -> { where(read_at: nil) }
    scope :read_messages, -> { where.not(read_at: nil) }

    enum :status, {
      pending: 0,
      in_progress: 1,
      resolved: 2,
      closed: 3
    }

    def mark_as_read!
      update!(read_at: Time.current) unless read?
    end

    def read?
      read_at.present?
    end

    def subject_label
      {
        "general" => "General Inquiry",
        "sales" => "Sales Question",
        "support" => "Technical Support",
        "partnership" => "Partnership Opportunity",
        "press" => "Press/Media Inquiry",
        "feature" => "Feature Request",
        "bug" => "Bug Report",
        "other" => "Other"
      }[subject] || subject.humanize
    end

    def estimated_response_time
      case subject
      when "sales"
        "4 hours"
      when "support"
        "24 hours"
      when "bug"
        "12 hours"
      else
        "24 hours"
      end
    end

    private

    def normalize_email
      self.email = email.to_s.downcase.strip if email.present?
    end

    def set_defaults
      self.status ||= "pending"
      self.submitted_at ||= Time.current
    end
end
