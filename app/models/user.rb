class User < ApplicationRecord
  # Acts As Tenant configuration - only for users with tenants
  acts_as_tenant :tenant, optional: true

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :omniauthable,
         omniauth_providers: [ :google_oauth2, :microsoft_graph ]

  # Associations
  belongs_to :tenant, optional: true

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :first_name, presence: true, length: { minimum: 1, maximum: 50 }
  validates :last_name, presence: true, length: { minimum: 1, maximum: 50 }
  validates :company, presence: true, length: { minimum: 1, maximum: 100 }
  validates :business_size, presence: true, inclusion: { in: %w[1-10 11-50 ************** 500+] }
  validates :terms_accepted, acceptance: true
  validates :role, inclusion: { in: %w[admin user manager owner], message: "%{value} is not a valid role" }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :admins, -> { where(role: "admin") }
  scope :managers, -> { where(role: "manager") }
  scope :regular_users, -> { where(role: "user") }

  # Callbacks
  before_validation :set_default_values, on: :create

  # Instance methods
  def admin?
    role == "admin"
  end

  def manager?
    role == "manager"
  end

  def regular_user?
    role == "user"
  end

  def active?
    active
  end

  def inactive?
    !active
  end

  def full_name
    "#{first_name} #{last_name}".strip
  end

  def display_name
    full_name.present? ? full_name : name
  end

  def initials
    if full_name.present?
      full_name.split.map(&:first).join.upcase.first(2)
    else
      name.split.map(&:first).join.upcase.first(2)
    end
  end

  def owner?
    role == "owner"
  end

  # Mock methods for features not yet implemented
  def avatar
    nil # Will be implemented when file uploads are added
  end

  def notifications
    # Mock notifications collection - will be replaced with actual implementation
    MockNotifications.new
  end

  def unread_notifications
    # Mock unread notifications collection - will be replaced with actual implementation
    MockNotifications.new
  end

  # Mock notification class until real notifications are implemented
  class MockNotifications
    def any?
      false
    end

    def count
      0
    end

    def recent
      self
    end

    def limit(num)
      []
    end

    def each
      # Empty iteration
    end
  end

  def deactivate!
    update!(active: false)
  end

  def activate!
    update!(active: true)
  end

  # Omniauth callback methods
  def self.from_omniauth(auth)
    where(email: auth.info.email).first_or_create do |user|
      user.email = auth.info.email
      user.name = auth.info.name
      user.first_name = auth.info.first_name || auth.info.name&.split(" ")&.first || "Unknown"
      user.last_name = auth.info.last_name || auth.info.name&.split(" ")&.last || "User"
      user.company = "Unknown Company" # Will need to be updated by user
      user.business_size = "1-10" # Default value
      user.terms_accepted = true # Assume they accepted terms during OAuth
      user.marketing_emails = false # Default to false for privacy
      user.password = Devise.friendly_token[0, 20]
      user.role = "user"
      user.active = true
    end
  end

  private

  def set_default_values
    self.role ||= "user"
    self.active = true if active.nil?
  end
end
