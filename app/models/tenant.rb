class Tenant < ApplicationRecord
  # Associations
  has_many :users, dependent: :destroy
  has_many :ahoy_visits, class_name: "Ahoy::Visit", dependent: :destroy
  has_many :ahoy_events, class_name: "Ahoy::Event", dependent: :destroy

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :subdomain, presence: true, uniqueness: { case_sensitive: false },
            format: { with: /\A[a-z0-9\-]+\z/, message: "can only contain lowercase letters, numbers, and hyphens" },
            length: { minimum: 3, maximum: 50 }
  validates :description, length: { maximum: 500 }, allow_blank: true

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }

  # Callbacks
  before_validation :normalize_subdomain
  before_validation :set_default_values, on: :create

  # Instance methods
  def active?
    active
  end

  def inactive?
    !active
  end

  def deactivate!
    update!(active: false)
  end

  def activate!
    update!(active: true)
  end

  def user_count
    users.count
  end

  def admin_users
    users.admins
  end

  def manager_users
    users.managers
  end

  def regular_users
    users.regular_users
  end

  def to_param
    subdomain
  end

  private

  def normalize_subdomain
    self.subdomain = subdomain&.downcase&.strip
  end

  def set_default_values
    self.active = true if active.nil?
  end
end
