class ContactMailer < ApplicationMailer
    default from: "<EMAIL>"

    def confirmation_email(contact_message)
      @contact_message = contact_message
      @estimated_response_time = @contact_message.estimated_response_time

      mail(
        to: @contact_message.email,
        subject: "We received your message - MarketingAI Support",
        template_path: "contact_mailer",
        template_name: "confirmation_email"
      )
    end

    def sales_notification(contact_message)
      @contact_message = contact_message

      mail(
        to: "<EMAIL>",
        subject: "[SALES] New inquiry from #{@contact_message.name}",
        template_path: "contact_mailer",
        template_name: "sales_notification"
      )
    end

    def support_notification(contact_message)
      @contact_message = contact_message

      mail(
        to: "<EMAIL>",
        subject: "[SUPPORT] #{@contact_message.subject_label} from #{@contact_message.name}",
        template_path: "contact_mailer",
        template_name: "support_notification"
      )
    end

    def partnership_notification(contact_message)
      @contact_message = contact_message

      mail(
        to: "<EMAIL>",
        subject: "[PARTNERSHIP] New opportunity from #{@contact_message.name}",
        template_path: "contact_mailer",
        template_name: "partnership_notification"
      )
    end

    def press_notification(contact_message)
      @contact_message = contact_message

      mail(
        to: "<EMAIL>",
        subject: "[PRESS] Media inquiry from #{@contact_message.name}",
        template_path: "contact_mailer",
        template_name: "press_notification"
      )
    end

    def general_notification(contact_message)
      @contact_message = contact_message

      mail(
        to: "<EMAIL>",
        subject: "[CONTACT] #{@contact_message.subject_label} from #{@contact_message.name}",
        template_path: "contact_mailer",
        template_name: "general_notification"
      )
    end
end
