class ContactMessagesController < ApplicationController
    skip_before_action :authenticate_user!
    skip_before_action :set_tenant
    skip_after_action :verify_authorized
    
    before_action :validate_contact_form, only: [:create]
    protect_from_forgery with: :null_session, only: [:create]
    
    def create
      @contact_message = ContactMessage.new(contact_message_params)
      @contact_message.ip_address = request.remote_ip
      @contact_message.user_agent = request.user_agent
      @contact_message.referrer = request.referer
      
      # Add UTM parameters from session if available
      if session[:page_visit]
        @contact_message.utm_source = session[:page_visit].dig(:utm_params, :utm_source)
        @contact_message.utm_medium = session[:page_visit].dig(:utm_params, :utm_medium)
        @contact_message.utm_campaign = session[:page_visit].dig(:utm_params, :utm_campaign)
      end
      
      if @contact_message.save
        # Send confirmation email and internal notification
        ContactMessageJob.perform_later(@contact_message.id)
        
        render json: { 
          success: true,
          message: "Thank you for your message! We'll get back to you within 24 hours.",
          contact_message_id: @contact_message.id
        }, status: :created
      else
        render json: { 
          success: false,
          message: "There was an error sending your message. Please try again.",
          errors: @contact_message.errors.messages
        }, status: :unprocessable_entity
      end
    rescue => e
      Rails.logger.error "Contact message error: #{e.message}"
      render json: { 
        success: false,
        message: "Something went wrong. Please try again later.",
        error_id: SecureRandom.uuid
      }, status: :internal_server_error
    end
    
    private
    
    def contact_message_params
      params.require(:contact_message).permit(
        :name, :email, :company, :subject, :message, :privacy_consent
      )
    end
    
    def validate_contact_form
      # Basic validation before model validation
      required_fields = [:name, :email, :message, :privacy_consent]
      missing_fields = required_fields.select { |field| params[:contact_message][field].blank? }
      
      if missing_fields.any?
        render json: { 
          success: false,
          message: "Please fill in all required fields.",
          missing_fields: missing_fields
        }, status: :unprocessable_entity
        return
      end
      
      # Email format validation
      email = params[:contact_message][:email]
      unless email.match?(URI::MailTo::EMAIL_REGEXP)
        render json: { 
          success: false,
          message: "Please enter a valid email address.",
          errors: { email: ["is not a valid email address"] }
        }, status: :unprocessable_entity
        return
      end
      
      # Privacy consent validation
      unless params[:contact_message][:privacy_consent] == "1"
        render json: { 
          success: false,
          message: "You must agree to the Privacy Policy and Terms of Service.",
          errors: { privacy_consent: ["must be accepted"] }
        }, status: :unprocessable_entity
        return
      end
    end
  end
  