class ContactMessagesController < ApplicationController
    skip_before_action :authenticate_user!
    skip_before_action :set_current_tenant
    skip_after_action :verify_authorized

    protect_from_forgery with: :null_session, only: [ :create ]

    def create
      @contact_message = ContactMessage.new(contact_message_params)
      @contact_message.ip_address = request.remote_ip || '127.0.0.1'
      @contact_message.user_agent = request.user_agent || 'Test Agent'
      @contact_message.referrer = request.referer

      # Add UTM parameters from session if available
      if session[:page_visit]
        @contact_message.utm_source = session[:page_visit].dig(:utm_params, :utm_source)
        @contact_message.utm_medium = session[:page_visit].dig(:utm_params, :utm_medium)
        @contact_message.utm_campaign = session[:page_visit].dig(:utm_params, :utm_campaign)
      end

      if @contact_message.save
        # Send confirmation email and internal notification
        ContactMessageJob.perform_later(@contact_message.id)

        render json: {
          status: "success",
          message: "Thank you for your message! We'll get back to you soon.",
          contact_message_id: @contact_message.id
        }, status: :created
      else
        Rails.logger.error "ContactMessage validation failed: #{@contact_message.errors.full_messages}"
        render json: {
          status: "error",
          message: "There was an error sending your message. Please try again.",
          errors: @contact_message.errors.messages
        }, status: :unprocessable_entity
      end
    rescue ActionController::ParameterMissing => e
      Rails.logger.error "Missing parameters: #{e.message}"
      render json: {
        status: "error",
        message: "Missing required fields: name, email, message, privacy_consent"
      }, status: :bad_request
    rescue => e
      Rails.logger.error "Contact message error: #{e.message}"
      render json: {
        status: "error",
        message: "Something went wrong. Please try again later.",
        error_id: SecureRandom.uuid
      }, status: :internal_server_error
    end

    private

    def contact_message_params
      params.require(:contact_message).permit(
        :name, :email, :company, :subject, :message, :privacy_consent
      )
    end
end
