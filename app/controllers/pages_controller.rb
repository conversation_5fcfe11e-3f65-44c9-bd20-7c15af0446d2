class PagesController < ApplicationController
  skip_before_action :authenticate_user!, only: [ 
    :index, :privacy, :terms, :about, :careers, :press, :partners, :investors,
    :help, :help_category, :help_article, :contact, :create_contact,
    :blog, :blog_post, :case_studies, :case_study, :webinars, :webinar,
    :whitepapers, :whitepaper, :roi_calculator, :calculate_roi,
    :onboarding, :tutorials, :tutorial, :community, :features, :pricing,
    :security, :enterprise, :cookies, :gdpr, :dpa
  ]
  skip_before_action :set_current_tenant, only: [ 
    :index, :privacy, :terms, :about, :careers, :press, :partners, :investors,
    :help, :help_category, :help_article, :contact, :create_contact,
    :blog, :blog_post, :case_studies, :case_study, :webinars, :webinar,
    :whitepapers, :whitepaper, :roi_calculator, :calculate_roi,
    :onboarding, :tutorials, :tutorial, :community, :features, :pricing,
    :security, :enterprise, :cookies, :gdpr, :dpa
  ]
  skip_after_action :verify_authorized, only: [ 
    :index, :privacy, :terms, :about, :careers, :press, :partners, :investors,
    :help, :help_category, :help_article, :contact, :create_contact,
    :blog, :blog_post, :case_studies, :case_study, :webinars, :webinar,
    :whitepapers, :whitepaper, :roi_calculator, :calculate_roi,
    :onboarding, :tutorials, :tutorial, :community, :features, :pricing,
    :security, :enterprise, :cookies, :gdpr, :dpa
  ]

  def index
    # Redirect logged-in users to dashboard
    if user_signed_in?
      redirect_to dashboard_path and return
    end

    # Track index page visit
    track_landing_page_visit

    # Set meta tags for SEO
    @page_title = "AI-Powered Marketing Automation Platform | MarketingAI"
    @page_description = "Transform your marketing with AI. Automate campaigns, generate compelling content, and optimize performance. Boost ROI by 300% in 30 days. Start free trial."
    @page_keywords = "AI marketing, marketing automation, email marketing, AI content generation, marketing ROI"
    @canonical_url = request.original_url

    # Open Graph data
    @og_title = "MarketingAI - Transform Your Marketing with AI"
    @og_description = "Automate campaigns, generate compelling content, and optimize performance with our enterprise-grade AI marketing platform."
    @og_image = ActionController::Base.helpers.asset_url("marketing_ai_og_image.svg")
    @og_url = request.original_url
    @og_type = "website"

    # Twitter Card data
    @twitter_card = "summary_large_image"
    @twitter_site = "@MarketingAI"
    @twitter_title = "MarketingAI - Transform Your Marketing with AI"
    @twitter_description = "Automate campaigns, generate compelling content, and optimize performance with AI."
    @twitter_image = ActionController::Base.helpers.asset_url("marketing_ai_twitter_card.svg")

    # Set structured data for rich snippets
    @structured_data = build_structured_data

    # A/B testing setup (if using a testing framework)
    setup_ab_testing if respond_to?(:setup_ab_testing, true)

    # Load any dynamic content
    @featured_testimonials = load_featured_testimonials
    @latest_blog_posts = load_latest_blog_posts

    # render "pages/index", layout: "index", status: :ok
  end

  def about
    # Track about page visit
    track_page_visit('about')
    
    # Set meta tags
    set_meta_tags(
      title: "About MarketingAI | Our Mission to Transform Marketing with AI",
      description: "Learn about MarketingAI's mission to democratize AI-powered marketing. Meet our team, discover our values, and see how we're building the future of marketing automation.",
      keywords: "about MarketingAI, AI marketing company, marketing automation team, company mission"
    )
    
    # Load team data (in production, this might come from a CMS or database)
    @team_members = load_team_members
    @company_stats = load_company_stats
    @awards = load_awards
    
    render 'pages/about', layout: 'application'
  end
  
  def contact
    # Track contact page visit
    track_page_visit('contact')
    
    # Set meta tags
    set_meta_tags(
      title: "Contact MarketingAI | Get Expert Marketing AI Support",
      description: "Get in touch with MarketingAI. Our expert team is ready to help you succeed with AI-powered marketing automation. Live chat, email, and phone support available.",
      keywords: "contact MarketingAI, marketing AI support, customer service, get help"
    )
    
    render 'pages/contact', layout: 'application'
  end
  
  def privacy
    set_meta_tags(
      title: "Privacy Policy | MarketingAI",
      description: "MarketingAI's privacy policy. Learn how we collect, use, and protect your personal information.",
      noindex: true
    )
    
    render 'pages/privacy'
  end
  
  def terms
    set_meta_tags(
      title: "Terms of Service | MarketingAI",
      description: "MarketingAI's terms of service. Read our terms and conditions for using our AI marketing platform.",
      noindex: true
    )
    
    render 'pages/terms'
  end


  def careers
    render "pages/careers"
  end

  def press
    render "pages/press"
  end

  def partners
    render "pages/partners"
  end

  def investors
    render "pages/investors"
  end

  # Support and Help pages
  def help
    render "pages/help"
  end

  def help_category
    @category = params[:category]
    render "pages/help_category"
  end

  def help_article
    @category = params[:category]
    @article = params[:article]
    render "pages/help_article"
  end

  def contact
    render "pages/contact"
  end

  def create_contact
    # TODO: Implement contact form submission
    redirect_to contact_path, notice: "Thank you for your message. We'll get back to you soon!"
  end

  # Educational Content pages
  def blog
    render "pages/blog"
  end

  def blog_post
    @slug = params[:slug]
    render "pages/blog_post"
  end

  def case_studies
    render "pages/case_studies"
  end

  def case_study
    @slug = params[:slug]
    render "pages/case_study"
  end

  def webinars
    render "pages/webinars"
  end

  def webinar
    @id = params[:id]
    render "pages/webinar"
  end

  def whitepapers
    render "pages/whitepapers"
  end

  def whitepaper
    @id = params[:id]
    render "pages/whitepaper"
  end

  # Tools
  def roi_calculator
    render "pages/roi_calculator"
  end

  def calculate_roi
    # TODO: Implement ROI calculation logic
    redirect_to roi_calculator_path, notice: "ROI calculation completed"
  end

  # Learning Resources
  def onboarding
    render "pages/onboarding"
  end

  def tutorials
    render "pages/tutorials"
  end

  def tutorial
    @slug = params[:slug]
    render "pages/tutorial"
  end

  def community
    render "pages/community"
  end

  # Product Information
  def features
    render "pages/features"
  end

  def pricing
    render "pages/pricing"
  end

  def security
    render "pages/security"
  end

  def enterprise
    render "pages/enterprise"
  end

  # Legal pages
  def cookies
    render "pages/cookies"
  end

  def gdpr
    render "pages/gdpr"
  end

  def dpa
    render "pages/dpa"
  end

  
  private
  
  def redirect_if_authenticated
    if user_signed_in?
      redirect_to dashboard_path, notice: "Welcome back!"
    end
  end
  
  def track_page_visit(page_name)
    analytics_data = {
      page: page_name,
      user_agent: request.user_agent,
      referrer: request.referer,
      utm_source: params[:utm_source],
      utm_medium: params[:utm_medium],
      utm_campaign: params[:utm_campaign],
      utm_content: params[:utm_content],
      utm_term: params[:utm_term],
      ip_address: request.remote_ip
    }
    
    # Store in session for conversion tracking
    session[:page_visit] = {
      page: page_name,
      timestamp: Time.current,
      utm_params: analytics_data.slice(:utm_source, :utm_medium, :utm_campaign, :utm_content, :utm_term)
    }
    
    # Async analytics tracking
    PageVisitAnalyticsJob.perform_later(analytics_data) if Rails.env.production?
  end
  
  def load_team_members
    # In production, this would come from your database or CMS
    [
      {
        name: "Alex Thompson",
        title: "Chief Executive Officer",
        bio: "Former VP of Marketing at TechCorp, Alex brings 15 years of marketing expertise and a passion for AI innovation.",
        avatar_url: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
        linkedin_url: "https://linkedin.com/in/alex-thompson",
        twitter_url: "https://twitter.com/alexthompson"
      },
      {
        name: "Sarah Chen",
        title: "Chief Technology Officer",
        bio: "AI researcher with PhD from Stanford. Led machine learning teams at Google and founded two successful AI startups.",
        avatar_url: "https://images.unsplash.com/photo-1494790108755-2616b612b77c?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
        linkedin_url: "https://linkedin.com/in/sarah-chen",
        github_url: "https://github.com/sarahchen"
      },
      # Add more team members...
    ]
  end
  
  def load_company_stats
    {
      customers: 50000,
      founded_year: 2019,
      team_members: 85,
      countries: 15,
      emails_sent: 2400000000,
      avg_roi_increase: 127
    }
  end
  
  def load_awards
    [
      {
        title: "Best AI Product 2023",
        organization: "TechCrunch Disrupt",
        year: 2023,
        icon_color: "yellow"
      },
      {
        title: "Innovation Award",
        organization: "Marketing Technology Awards",
        year: 2023,
        icon_color: "blue"
      },
      {
        title: "Top 50 Startups",
        organization: "Forbes",
        year: 2022,
        icon_color: "green"
      },
      {
        title: "Best Customer Support",
        organization: "G2 Users Choice",
        year: 2023,
        icon_color: "purple"
      }
    ]
  end

  def redirect_if_authenticated
    if user_signed_in?
      redirect_to dashboard_path, notice: "Welcome back!"
    end
  end

  def track_landing_page_visit
    # Track in analytics
    analytics_data = {
      page: "landing",
      user_agent: request.user_agent,
      referrer: request.referer,
      utm_source: params[:utm_source],
      utm_medium: params[:utm_medium],
      utm_campaign: params[:utm_campaign],
      utm_content: params[:utm_content],
      utm_term: params[:utm_term]
    }

    # Store in session for later conversion tracking
    session[:landing_visit] = {
      timestamp: Time.current,
      utm_params: analytics_data.slice(:utm_source, :utm_medium, :utm_campaign, :utm_content, :utm_term)
    }

    # Async analytics tracking
    LandingPageAnalyticsJob.perform_later(analytics_data) if Rails.env.production?
  end

  def build_structured_data
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "MarketingAI",
      "description": "AI-powered marketing automation platform that helps businesses automate campaigns, generate content, and optimize performance.",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "49",
        "priceCurrency": "USD",
        "priceValidUntil": 1.year.from_now.strftime("%Y-%m-%d")
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "ratingCount": "1200",
        "bestRating": "5"
      },
      "provider": {
        "@type": "Organization",
        "name": "MarketingAI",
        "url": root_url
      }
    }
  end

  def load_featured_testimonials
    # In production, this would load from your database
    # For now, return static data
    [
      {
        name: "Sarah Johnson",
        title: "Marketing Director",
        company: "TechCorp",
        content: "MarketingAI transformed our email marketing. We saw a 300% increase in engagement within the first month.",
        rating: 5,
        avatar_url: "https://images.unsplash.com/photo-1494790108755-2616b612b77c?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
      },
      {
        name: "Michael Chen",
        title: "CEO",
        company: "StartupXYZ",
        content: "The automation features are incredible. Our conversion rates doubled, and we're spending 50% less time on manual campaign management.",
        rating: 5,
        avatar_url: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
      }
    ]
  end

  def load_latest_blog_posts
    # In production, load recent blog posts
    # For now, return empty array
    []
  end
end
