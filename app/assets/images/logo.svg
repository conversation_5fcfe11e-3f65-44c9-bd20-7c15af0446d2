<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#logoGradient)" stroke="white" stroke-width="16"/>
  
  <!-- AI Text -->
  <text x="256" y="220" font-family="Arial, sans-serif" font-size="120" font-weight="bold" text-anchor="middle" fill="white">
    AI
  </text>
  
  <!-- Marketing subtitle -->
  <text x="256" y="300" font-family="Arial, sans-serif" font-size="36" font-weight="normal" text-anchor="middle" fill="white" opacity="0.9">
    Marketing
  </text>
  
  <!-- Decorative elements -->
  <circle cx="150" cy="150" r="20" fill="white" opacity="0.3"/>
  <circle cx="362" cy="150" r="20" fill="white" opacity="0.3"/>
  <circle cx="150" cy="362" r="20" fill="white" opacity="0.3"/>
  <circle cx="362" cy="362" r="20" fill="white" opacity="0.3"/>
  
  <!-- Neural network lines -->
  <line x1="150" y1="150" x2="256" y2="256" stroke="white" stroke-width="2" opacity="0.4"/>
  <line x1="362" y1="150" x2="256" y2="256" stroke="white" stroke-width="2" opacity="0.4"/>
  <line x1="150" y1="362" x2="256" y2="256" stroke="white" stroke-width="2" opacity="0.4"/>
  <line x1="362" y1="362" x2="256" y2="256" stroke="white" stroke-width="2" opacity="0.4"/>
</svg>
