<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:0.4" />
    </linearGradient>
    <pattern id="dots" patternUnits="userSpaceOnUse" width="60" height="60">
      <circle cx="30" cy="30" r="2" fill="white" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#heroGradient)"/>
  <rect width="100%" height="100%" fill="url(#dots)"/>
  
  <!-- Floating geometric shapes -->
  <circle cx="200" cy="200" r="60" fill="white" opacity="0.05"/>
  <rect x="1600" y="150" width="120" height="120" fill="white" opacity="0.05" rx="20"/>
  <circle cx="300" cy="800" r="80" fill="white" opacity="0.05"/>
  <rect x="1400" y="750" width="100" height="100" fill="white" opacity="0.05" rx="15"/>
  
  <!-- Central focus area -->
  <circle cx="960" cy="540" r="300" fill="white" opacity="0.03"/>
  
  <!-- Tech grid pattern -->
  <defs>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="white" stroke-width="1" opacity="0.05"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>
</svg>
